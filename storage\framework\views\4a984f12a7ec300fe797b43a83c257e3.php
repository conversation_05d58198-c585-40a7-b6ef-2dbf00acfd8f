<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'path' => '',
    'alt' => '',
    'class' => '',
    'loading' => 'lazy',
    'width' => null,
    'height' => null,
    'thumbnail' => false,
    'preferWebp' => true
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'path' => '',
    'alt' => '',
    'class' => '',
    'loading' => 'lazy',
    'width' => null,
    'height' => null,
    'thumbnail' => false,
    'preferWebp' => true
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $imageService = app(\App\Services\ImageService::class);
    
    if ($thumbnail) {
        $urls = $imageService->getThumbnailUrl($path, $preferWebp);
    } else {
        $urls = $imageService->getImageUrl($path, $preferWebp);
    }
    
    $webpUrl = $urls['webp'] ?? null;
    $originalUrl = $urls['original'];
?>

<?php if (isset($component)) { $__componentOriginal791b30882bf4ea366ded2c237c9dabcb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal791b30882bf4ea366ded2c237c9dabcb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lazy-image','data' => ['src' => $originalUrl,'webp' => $webpUrl,'alt' => $alt,'class' => $class,'loading' => $loading,'width' => $width,'height' => $height,'attributes' => $attributes->except(['path', 'alt', 'class', 'loading', 'width', 'height', 'thumbnail', 'preferWebp'])]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('lazy-image'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['src' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($originalUrl),'webp' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($webpUrl),'alt' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($alt),'class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($class),'loading' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($loading),'width' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($width),'height' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($height),'attributes' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($attributes->except(['path', 'alt', 'class', 'loading', 'width', 'height', 'thumbnail', 'preferWebp']))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal791b30882bf4ea366ded2c237c9dabcb)): ?>
<?php $attributes = $__attributesOriginal791b30882bf4ea366ded2c237c9dabcb; ?>
<?php unset($__attributesOriginal791b30882bf4ea366ded2c237c9dabcb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal791b30882bf4ea366ded2c237c9dabcb)): ?>
<?php $component = $__componentOriginal791b30882bf4ea366ded2c237c9dabcb; ?>
<?php unset($__componentOriginal791b30882bf4ea366ded2c237c9dabcb); ?>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\new-portfolio\resources\views/components/optimized-image.blade.php ENDPATH**/ ?>