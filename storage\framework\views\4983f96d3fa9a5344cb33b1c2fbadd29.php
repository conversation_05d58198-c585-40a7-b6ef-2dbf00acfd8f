

<?php $__env->startSection('title', 'Projects - ' . ($siteBranding['title'] ?? '<PERSON>')); ?>
<?php $__env->startSection('meta_description', 'Browse my portfolio of web development and 3D modeling projects. Featuring Laravel applications, 3ds Max models, and creative digital solutions.'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">My Projects</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Explore my portfolio of web development and 3D modeling projects. Each project represents a unique challenge and creative solution.
            </p>
        </div>

        <!-- Filter Section -->
        <div class="bg-white rounded-lg shadow-soft p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <h2 class="text-lg font-semibold text-gray-900">Filter Projects</h2>
                
                <div class="flex flex-col sm:flex-row gap-4">
                    <!-- Technology Filter -->
                    <div class="flex-1">
                        <label for="technology-filter" class="block text-sm font-medium text-gray-700 mb-2">
                            Filter by Technology
                        </label>
                        <select id="technology-filter" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Technologies</option>
                            <?php $__currentLoopData = $technologies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $technology): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($technology); ?>" 
                                        <?php echo e(request('technology') === $technology ? 'selected' : ''); ?>>
                                    <?php echo e($technology); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Tag Filter -->
                    <div class="flex-1">
                        <label for="tag-filter" class="block text-sm font-medium text-gray-700 mb-2">
                            Filter by Tag
                        </label>
                        <select id="tag-filter" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Tags</option>
                            <?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($tag); ?>" 
                                        <?php echo e(request('tag') === $tag ? 'selected' : ''); ?>>
                                    <?php echo e($tag); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Clear Filters Button -->
                    <?php if(request('technology') || request('tag')): ?>
                        <div class="flex items-end">
                            <a href="<?php echo e(route('projects.index')); ?>" 
                               class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                                Clear Filters
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Active Filters Display -->
            <?php if(request('technology') || request('tag')): ?>
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-700">Active filters:</span>
                        <?php if(request('technology')): ?>
                            <span class="px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full">
                                Technology: <?php echo e(request('technology')); ?>

                            </span>
                        <?php endif; ?>
                        <?php if(request('tag')): ?>
                            <span class="px-3 py-1 bg-secondary-100 text-secondary-700 text-sm rounded-full">
                                Tag: <?php echo e(request('tag')); ?>

                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Projects Grid -->
        <?php if($projects->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if (isset($component)) { $__componentOriginaldbcceabf4a99a34f9999233ae1fef693 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldbcceabf4a99a34f9999233ae1fef693 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.project-card','data' => ['project' => $project]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('project-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['project' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($project)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldbcceabf4a99a34f9999233ae1fef693)): ?>
<?php $attributes = $__attributesOriginaldbcceabf4a99a34f9999233ae1fef693; ?>
<?php unset($__attributesOriginaldbcceabf4a99a34f9999233ae1fef693); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldbcceabf4a99a34f9999233ae1fef693)): ?>
<?php $component = $__componentOriginaldbcceabf4a99a34f9999233ae1fef693; ?>
<?php unset($__componentOriginaldbcceabf4a99a34f9999233ae1fef693); ?>
<?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <!-- No Projects Found -->
            <div class="text-center py-12">
                <div class="max-w-md mx-auto">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                    <h3 class="mt-4 text-lg font-medium text-gray-900">No projects found</h3>
                    <p class="mt-2 text-gray-500">
                        <?php if(request('technology') || request('tag')): ?>
                            No projects match your current filters. Try adjusting your search criteria.
                        <?php else: ?>
                            There are no published projects available at the moment.
                        <?php endif; ?>
                    </p>
                    <?php if(request('technology') || request('tag')): ?>
                        <a href="<?php echo e(route('projects.index')); ?>" 
                           class="mt-4 inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors duration-200">
                            View All Projects
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const technologyFilter = document.getElementById('technology-filter');
    const tagFilter = document.getElementById('tag-filter');
    
    function updateFilters() {
        const technology = technologyFilter.value;
        const tag = tagFilter.value;
        
        const url = new URL(window.location.href);
        url.searchParams.delete('technology');
        url.searchParams.delete('tag');
        
        if (technology) {
            url.searchParams.set('technology', technology);
        }
        if (tag) {
            url.searchParams.set('tag', tag);
        }
        
        window.location.href = url.toString();
    }
    
    technologyFilter.addEventListener('change', updateFilters);
    tagFilter.addEventListener('change', updateFilters);
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new-portfolio\resources\views/pages/projects/index.blade.php ENDPATH**/ ?>