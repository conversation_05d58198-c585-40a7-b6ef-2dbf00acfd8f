<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\BlogRequest;
use App\Models\Blog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BlogController extends Controller
{
    /**
     * Display a listing of the blog posts.
     */
    public function index(Request $request)
    {
        $query = Blog::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'scheduled') {
                $query->scheduled();
            } else {
                $query->where('status', $status);
            }
        }

        // Tag filter
        if ($request->filled('tag')) {
            $query->byTag($request->get('tag'));
        }

        // Sort by
        $sortBy = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        if (in_array($sortBy, ['title', 'status', 'published_at', 'created_at'])) {
            $query->orderBy($sortBy, $sortDirection);
        }

        $blogs = $query->paginate(15)->withQueryString();

        // Get all unique tags for filter dropdown
        $allTags = Blog::whereNotNull('tags')
            ->get()
            ->pluck('tags')
            ->flatten()
            ->unique()
            ->sort()
            ->values();

        return view('admin.blog.index', compact('blogs', 'allTags'));
    }

    /**
     * Show the form for creating a new blog post.
     */
    public function create()
    {
        return view('admin.blog.create');
    }

    /**
     * Store a newly created blog post in storage.
     */
    public function store(BlogRequest $request)
    {
        try {
            $data = $request->validated();

            // Handle thumbnail upload
            if ($request->hasFile('thumbnail')) {
                $thumbnailPath = $this->handleImageUpload($request->file('thumbnail'));
                $data['thumbnail'] = $thumbnailPath;
            }

            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = Blog::generateUniqueSlug($data['title']);
            }

            $blog = Blog::create($data);

            return redirect()
                ->route('admin.blog.index')
                ->with('success', 'Blog post created successfully!');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to create blog post. Please try again.');
        }
    }

    /**
     * Display the specified blog post.
     */
    public function show(Blog $blog)
    {
        return view('admin.blog.show', compact('blog'));
    }

    /**
     * Show the form for editing the specified blog post.
     */
    public function edit(Blog $blog)
    {
        return view('admin.blog.edit', compact('blog'));
    }

    /**
     * Update the specified blog post in storage.
     */
    public function update(BlogRequest $request, Blog $blog)
    {
        try {
            $data = $request->validated();

            // Handle thumbnail upload
            if ($request->hasFile('thumbnail')) {
                // Delete old thumbnail if exists
                if ($blog->thumbnail) {
                    $this->deleteImage($blog->thumbnail);
                }
                
                $thumbnailPath = $this->handleImageUpload($request->file('thumbnail'));
                $data['thumbnail'] = $thumbnailPath;
            }

            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = Blog::generateUniqueSlug($data['title']);
            }

            $blog->update($data);

            return redirect()
                ->route('admin.blog.index')
                ->with('success', 'Blog post updated successfully!');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to update blog post. Please try again.');
        }
    }

    /**
     * Remove the specified blog post from storage.
     */
    public function destroy(Blog $blog)
    {
        try {
            // Delete thumbnail if exists
            if ($blog->thumbnail) {
                $this->deleteImage($blog->thumbnail);
            }

            $blog->delete();

            return redirect()
                ->route('admin.blog.index')
                ->with('success', 'Blog post deleted successfully!');

        } catch (\Exception $e) {
            return back()
                ->with('error', 'Failed to delete blog post. Please try again.');
        }
    }

    /**
     * Handle bulk actions for blog posts.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,publish,draft',
            'selected_blogs' => 'required|array|min:1',
            'selected_blogs.*' => 'exists:blogs,id',
        ]);

        $blogIds = $request->get('selected_blogs');
        $action = $request->get('action');

        try {
            switch ($action) {
                case 'delete':
                    $blogs = Blog::whereIn('id', $blogIds)->get();
                    foreach ($blogs as $blog) {
                        if ($blog->thumbnail) {
                            $this->deleteImage($blog->thumbnail);
                        }
                    }
                    Blog::whereIn('id', $blogIds)->delete();
                    $message = 'Selected blog posts deleted successfully!';
                    break;

                case 'publish':
                    Blog::whereIn('id', $blogIds)->update([
                        'status' => 'published',
                        'published_at' => now(),
                    ]);
                    $message = 'Selected blog posts published successfully!';
                    break;

                case 'draft':
                    Blog::whereIn('id', $blogIds)->update([
                        'status' => 'draft',
                        'published_at' => null,
                    ]);
                    $message = 'Selected blog posts moved to draft successfully!';
                    break;
            }

            return back()->with('success', $message);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to perform bulk action. Please try again.');
        }
    }

    /**
     * Handle image upload.
     */
    private function handleImageUpload($file)
    {
        $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        $path = 'images/blog/' . $filename;
        
        // Move file to public/images/blog directory
        $file->move(public_path('images/blog'), $filename);
        
        return $path;
    }

    /**
     * Delete image file.
     */
    private function deleteImage($imagePath)
    {
        $fullPath = public_path($imagePath);
        if (file_exists($fullPath)) {
            unlink($fullPath);
        }
    }
}