/**
 * Accessibility enhancements for the portfolio website
 */

// Global accessibility utilities
window.A11y = {
    // Announce messages to screen readers
    announce: function(message, priority = 'polite') {
        const announcer = document.getElementById('sr-announcements');
        if (announcer) {
            announcer.setAttribute('aria-live', priority);
            announcer.textContent = message;
            
            // Clear after announcement
            setTimeout(() => {
                announcer.textContent = '';
            }, 1000);
        }
    },

    // Focus management utilities
    focus: {
        // Trap focus within an element
        trap: function(element) {
            const focusableElements = element.querySelectorAll(
                'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select, [tabindex]:not([tabindex="-1"])'
            );
            
            if (focusableElements.length === 0) return;
            
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];
            
            element.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    if (e.shiftKey) {
                        if (document.activeElement === firstElement) {
                            e.preventDefault();
                            lastElement.focus();
                        }
                    } else {
                        if (document.activeElement === lastElement) {
                            e.preventDefault();
                            firstElement.focus();
                        }
                    }
                }
            });
        },

        // Set focus to element with announcement
        set: function(element, message) {
            if (element) {
                element.focus();
                if (message) {
                    this.announce(message);
                }
            }
        }
    },

    // Keyboard navigation helpers
    keyboard: {
        // Handle arrow key navigation in lists
        handleArrowNavigation: function(container, itemSelector) {
            const items = Array.from(container.querySelectorAll(itemSelector));
            
            container.addEventListener('keydown', function(e) {
                const currentIndex = items.indexOf(document.activeElement);
                let nextIndex;
                
                switch (e.key) {
                    case 'ArrowDown':
                    case 'ArrowRight':
                        e.preventDefault();
                        nextIndex = (currentIndex + 1) % items.length;
                        items[nextIndex].focus();
                        break;
                    case 'ArrowUp':
                    case 'ArrowLeft':
                        e.preventDefault();
                        nextIndex = currentIndex <= 0 ? items.length - 1 : currentIndex - 1;
                        items[nextIndex].focus();
                        break;
                    case 'Home':
                        e.preventDefault();
                        items[0].focus();
                        break;
                    case 'End':
                        e.preventDefault();
                        items[items.length - 1].focus();
                        break;
                }
            });
        }
    },

    // Color contrast and theme utilities
    theme: {
        // Check if user prefers high contrast
        prefersHighContrast: function() {
            return window.matchMedia('(prefers-contrast: high)').matches;
        },

        // Check if user prefers reduced motion
        prefersReducedMotion: function() {
            return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        },

        // Apply high contrast styles if needed
        applyHighContrastIfNeeded: function() {
            if (this.prefersHighContrast()) {
                document.documentElement.classList.add('high-contrast');
            }
        }
    },

    // Form accessibility enhancements
    forms: {
        // Enhance form validation with better accessibility
        enhanceValidation: function(form) {
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                // Add live validation feedback
                input.addEventListener('blur', function() {
                    if (this.checkValidity()) {
                        this.setAttribute('aria-invalid', 'false');
                        // Remove error styling
                        this.classList.remove('border-red-500');
                    } else {
                        this.setAttribute('aria-invalid', 'true');
                        // Add error styling
                        this.classList.add('border-red-500');
                    }
                });

                // Clear validation on input
                input.addEventListener('input', function() {
                    if (this.getAttribute('aria-invalid') === 'true') {
                        if (this.checkValidity()) {
                            this.setAttribute('aria-invalid', 'false');
                            this.classList.remove('border-red-500');
                        }
                    }
                });
            });
        }
    },

    // Image accessibility enhancements
    images: {
        // Add loading states and error handling for images
        enhanceImages: function() {
            const images = document.querySelectorAll('img[loading="lazy"]');
            
            images.forEach(img => {
                // Add loading indicator
                img.addEventListener('loadstart', function() {
                    this.setAttribute('aria-busy', 'true');
                });

                // Remove loading indicator when loaded
                img.addEventListener('load', function() {
                    this.removeAttribute('aria-busy');
                });

                // Handle load errors
                img.addEventListener('error', function() {
                    this.removeAttribute('aria-busy');
                    this.setAttribute('aria-label', 'Image failed to load');
                });
            });
        }
    }
};

// Initialize accessibility features when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Apply high contrast theme if needed
    A11y.theme.applyHighContrastIfNeeded();

    // Enhance all forms
    const forms = document.querySelectorAll('form');
    forms.forEach(form => A11y.forms.enhanceValidation(form));

    // Enhance images
    A11y.images.enhanceImages();

    // Add keyboard navigation to card grids
    const cardGrids = document.querySelectorAll('.grid');
    cardGrids.forEach(grid => {
        const cards = grid.querySelectorAll('article a, .card a');
        if (cards.length > 0) {
            A11y.keyboard.handleArrowNavigation(grid, 'article a, .card a');
        }
    });

    // Enhance skip links
    const skipLinks = document.querySelectorAll('a[href^="#"]');
    skipLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({ behavior: 'smooth' });
                target.focus();
                A11y.announce(`Navigated to ${target.textContent || target.getAttribute('aria-label') || 'section'}`);
            }
        });
    });

    // Add focus indicators for keyboard users
    let isUsingKeyboard = false;
    
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            isUsingKeyboard = true;
            document.body.classList.add('keyboard-navigation');
        }
    });
    
    document.addEventListener('mousedown', function() {
        isUsingKeyboard = false;
        document.body.classList.remove('keyboard-navigation');
    });

    // Handle orientation changes on mobile
    window.addEventListener('orientationchange', function() {
        // Announce orientation change to screen readers
        setTimeout(() => {
            const orientation = window.orientation === 0 || window.orientation === 180 ? 'portrait' : 'landscape';
            A11y.announce(`Screen orientation changed to ${orientation}`);
        }, 500);
    });

    // Handle reduced motion preferences
    if (A11y.theme.prefersReducedMotion()) {
        document.documentElement.classList.add('reduce-motion');
    }

    // Monitor for changes in motion preferences
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    motionQuery.addEventListener('change', function(e) {
        if (e.matches) {
            document.documentElement.classList.add('reduce-motion');
        } else {
            document.documentElement.classList.remove('reduce-motion');
        }
    });
});

// Export for use in other modules
export default A11y;