<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['project']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['project']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<article class="bg-white rounded-lg shadow-soft overflow-hidden hover:shadow-medium focus-within:shadow-medium transition-all duration-300 group">
    <!-- Project Thumbnail -->
    <div class="h-48 sm:h-52 md:h-48 lg:h-52 bg-gradient-to-br from-primary-100 to-primary-200 relative overflow-hidden">
        <?php if($project->thumbnail): ?>
            <?php if (isset($component)) { $__componentOriginal69176bc01f29785a9e16a2a1923b2050 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69176bc01f29785a9e16a2a1923b2050 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.optimized-image','data' => ['path' => $project->thumbnail,'alt' => $project->title . ' project thumbnail','class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-300','thumbnail' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('optimized-image'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['path' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($project->thumbnail),'alt' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($project->title . ' project thumbnail'),'class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-300','thumbnail' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69176bc01f29785a9e16a2a1923b2050)): ?>
<?php $attributes = $__attributesOriginal69176bc01f29785a9e16a2a1923b2050; ?>
<?php unset($__attributesOriginal69176bc01f29785a9e16a2a1923b2050); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69176bc01f29785a9e16a2a1923b2050)): ?>
<?php $component = $__componentOriginal69176bc01f29785a9e16a2a1923b2050; ?>
<?php unset($__componentOriginal69176bc01f29785a9e16a2a1923b2050); ?>
<?php endif; ?>
        <?php else: ?>
            <!-- Placeholder gradient -->
            <div class="w-full h-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center group-hover:from-primary-200 group-hover:to-primary-300 transition-colors duration-300">
                <svg class="w-12 h-12 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Project Content -->
    <div class="p-4 sm:p-6">
        <h3 class="text-lg sm:text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
            <?php echo e($project->title); ?>

        </h3>
        <p class="text-gray-600 mb-4 line-clamp-2 text-sm sm:text-base leading-relaxed">
            <?php echo e($project->description); ?>

        </p>
        
        <!-- Technologies/Tags -->
        <?php if($project->technologies && count($project->technologies) > 0): ?>
            <div class="flex flex-wrap gap-2 mb-4" role="list" aria-label="Technologies used">
                <?php $__currentLoopData = array_slice($project->technologies, 0, 3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tech): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <span class="px-2 sm:px-3 py-1 bg-primary-100 text-primary-700 text-xs sm:text-sm rounded-full font-medium" role="listitem">
                        <?php echo e($tech); ?>

                    </span>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php if(count($project->technologies) > 3): ?>
                    <span class="px-2 sm:px-3 py-1 bg-gray-100 text-gray-600 text-xs sm:text-sm rounded-full font-medium" 
                          role="listitem"
                          title="<?php echo e(implode(', ', array_slice($project->technologies, 3))); ?>">
                        +<?php echo e(count($project->technologies) - 3); ?> more
                    </span>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <!-- View Project Link -->
        <a href="<?php echo e(route('projects.show', $project->slug)); ?>" 
           class="inline-flex items-center text-primary-600 hover:text-primary-700 focus:text-primary-700 font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md text-sm sm:text-base touch-manipulation"
           aria-label="View details for <?php echo e($project->title); ?> project">
            <span>View Project</span>
            <svg class="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
            </svg>
        </a>
    </div>
</article><?php /**PATH C:\xampp\htdocs\new-portfolio\resources\views/components/project-card.blade.php ENDPATH**/ ?>