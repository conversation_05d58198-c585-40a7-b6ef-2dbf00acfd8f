<?php

namespace App\Http\Controllers;

use App\Models\Testimonial;
use App\Services\SEOService;
use Illuminate\Http\Request;

class TestimonialController extends Controller
{
    protected $seoService;

    public function __construct(SEOService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display a listing of testimonials.
     */
    public function index()
    {
        $testimonials = Testimonial::ordered()
            ->get();

        // Generate SEO meta tags
        $seoData = $this->seoService->generateMetaTags('testimonials');

        return view('pages.testimonials', compact('testimonials', 'seoData'));
    }
}