<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use App\Services\SEOService;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    protected $seoService;

    public function __construct(SEOService $seoService)
    {
        $this->seoService = $seoService;
    }

    public function index()
    {
        $blogs = Blog::published()
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        // Generate SEO meta tags
        $seoData = $this->seoService->generateMetaTags('blog');

        return view('pages.blog.index', compact('blogs', 'seoData'));
    }

    public function show(Blog $blog)
    {
        // Check if blog is published
        if (!$blog->isPublished()) {
            abort(404);
        }

        // Get related articles (same tags, excluding current)
        $relatedBlogs = Blog::published()
            ->where('id', '!=', $blog->id)
            ->where(function ($query) use ($blog) {
                if ($blog->tags) {
                    foreach ($blog->tags as $tag) {
                        $query->orWhereJsonContains('tags', $tag);
                    }
                }
            })
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        // Get previous and next blog posts for navigation
        $previousBlog = Blog::published()
            ->where('published_at', '<', $blog->published_at)
            ->orderBy('published_at', 'desc')
            ->first();

        $nextBlog = Blog::published()
            ->where('published_at', '>', $blog->published_at)
            ->orderBy('published_at', 'asc')
            ->first();

        // Generate SEO meta tags for this specific blog post
        $seoData = $this->seoService->generateMetaTags('blog', $blog);

        return view('pages.blog.show', compact('blog', 'relatedBlogs', 'previousBlog', 'nextBlog', 'seoData'));
    }
}