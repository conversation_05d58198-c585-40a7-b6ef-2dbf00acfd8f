@extends('layouts.admin')

@section('title', 'General Settings')

@section('content')
<div class="space-y-6">
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">General Settings</h1>
            <p class="text-sm text-gray-600">Manage Hero Section and Footer Content</p>
        </div>
        <a href="{{ route('admin.content.index') }}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            ← Back to Content
        </a>
    </div>



    <form action="{{ route('admin.content.general-bulk-update') }}" method="POST" class="space-y-6">
        @csrf
        
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">General Content Sections</h2>
                <p class="text-sm text-gray-600">Configure your site's main content areas</p>
            </div>
            
            <div class="p-6 space-y-8">
                @foreach($generalSections as $key => $title)
                    @php
                        $content = $contents->get($key);
                    @endphp
                    
                    <div class="border border-gray-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">{{ $title }}</h3>
                            @if($content && $content->updated_at)
                                <span class="text-sm text-gray-500">
                                    Last updated: {{ $content->updated_at->format('M d, Y \a\t g:i A') }}
                                </span>
                            @endif
                        </div>
                        
                        <div class="space-y-4">
                            <!-- Title Field -->
                            <div>
                                <label for="sections_{{ $key }}_title" class="block text-sm font-medium text-gray-700 mb-2">
                                    Title
                                </label>
                                <input type="text" 
                                       name="sections[{{ $key }}][title]" 
                                       id="sections_{{ $key }}_title"
                                       value="{{ old("sections.{$key}.title", $content->title ?? '') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Enter {{ strtolower($title) }} title">
                            </div>
                            
                            <!-- Content Field -->
                            <div>
                                <label for="sections_{{ $key }}_content" class="block text-sm font-medium text-gray-700 mb-2">
                                    Content
                                </label>
                                <textarea name="sections[{{ $key }}][content]" 
                                          id="sections_{{ $key }}_content"
                                          rows="6"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                          placeholder="Enter {{ strtolower($title) }} content">{{ old("sections.{$key}.content", $content->content ?? '') }}</textarea>
                            </div>
                            
                            @if($key === 'hero')
                                <!-- Hero-specific meta fields -->
                                <div class="space-y-4">
                                    <div>
                                        <label for="sections_{{ $key }}_subtitle" class="block text-sm font-medium text-gray-700 mb-2">
                                            Subtitle <span class="text-gray-500">(appears below main title)</span>
                                        </label>
                                        <input type="text" 
                                               name="sections[{{ $key }}][meta][subtitle]" 
                                               id="sections_{{ $key }}_subtitle"
                                               value="{{ old("sections.{$key}.meta.subtitle", $content->meta['subtitle'] ?? '2D/3D Artist & Web Developer') }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="2D/3D Artist & Web Developer">
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="sections_{{ $key }}_cta_text" class="block text-sm font-medium text-gray-700 mb-2">
                                                Primary Button Text
                                            </label>
                                            <input type="text" 
                                                   name="sections[{{ $key }}][meta][cta_text]" 
                                                   id="sections_{{ $key }}_cta_text"
                                                   value="{{ old("sections.{$key}.meta.cta_text", $content->meta['cta_text'] ?? 'View Projects') }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                   placeholder="View Projects">
                                        </div>
                                        <div>
                                            <label for="sections_{{ $key }}_cta_url" class="block text-sm font-medium text-gray-700 mb-2">
                                                Primary Button URL
                                            </label>
                                            <input type="url" 
                                                   name="sections[{{ $key }}][meta][cta_url]" 
                                                   id="sections_{{ $key }}_cta_url"
                                                   value="{{ old("sections.{$key}.meta.cta_url", $content->meta['cta_url'] ?? '') }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                   placeholder="/projects">
                                        </div>
                                    </div>
                                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-blue-800">
                                                    <strong>Title:</strong> Main hero heading (supports HTML like &lt;span class="text-primary-600"&gt;Name&lt;/span&gt;)<br>
                                                    <strong>Content:</strong> Description text below the subtitle<br>
                                                    <strong>Secondary button:</strong> "Need Help?" button always links to contact page
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                            
                            @if($key === 'footer')
                                <!-- Footer-specific meta fields -->
                                <div class="space-y-4">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="sections_{{ $key }}_copyright" class="block text-sm font-medium text-gray-700 mb-2">
                                                Copyright Text
                                            </label>
                                            <input type="text" 
                                                   name="sections[{{ $key }}][meta][copyright]" 
                                                   id="sections_{{ $key }}_copyright"
                                                   value="{{ old("sections.{$key}.meta.copyright", $content->meta['copyright'] ?? '© ' . date('Y') . ' Aziz Khan. All rights reserved.') }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                   placeholder="© {{ date('Y') }} Your Name. All rights reserved.">
                                        </div>
                                        <div>
                                            <label for="sections_{{ $key }}_email" class="block text-sm font-medium text-gray-700 mb-2">
                                                Contact Email
                                            </label>
                                            <input type="email" 
                                                   name="sections[{{ $key }}][meta][email]" 
                                                   id="sections_{{ $key }}_email"
                                                   value="{{ old("sections.{$key}.meta.email", $content->meta['email'] ?? '<EMAIL>') }}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                   placeholder="<EMAIL>">
                                        </div>
                                    </div>
                                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-blue-800">
                                                    <strong>Title:</strong> Brand name displayed in footer<br>
                                                    <strong>Content:</strong> Brief description about you/your business<br>
                                                    <strong>Email:</strong> Contact email shown in footer and used for mailto links
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
        
        <div class="flex justify-end space-x-3">
            <a href="{{ route('admin.content.index') }}" 
               class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save General Settings
            </button>
        </div>
    </form>
</div>
@endsection