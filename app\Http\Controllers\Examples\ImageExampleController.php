<?php

namespace App\Http\Controllers\Examples;

use App\Http\Controllers\Controller;
use App\Http\Requests\ImageUploadRequest;
use App\Services\ImageService;
use Illuminate\Http\Request;

/**
 * Example controller demonstrating image handling functionality
 * This is for documentation purposes and can be removed in production
 */
class ImageExampleController extends Controller
{
    protected ImageService $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Example: Single image upload
     */
    public function uploadSingle(ImageUploadRequest $request)
    {
        try {
            $file = $request->file('image');
            
            // Upload to 'examples' directory
            $paths = $this->imageService->uploadImage($file, 'examples');
            
            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'paths' => $paths
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Example: Multiple image upload with custom sizes
     */
    public function uploadMultiple(Request $request)
    {
        $request->validate([
            'images.*' => 'required|image|mimes:jpeg,jpg,png,gif,webp|max:5120'
        ]);

        $uploadedImages = [];
        
        foreach ($request->file('images') as $file) {
            try {
                // Define custom sizes for this upload
                $customSizes = [
                    'small' => ['width' => 300, 'height' => 200],
                    'medium' => ['width' => 600, 'height' => 400],
                    'large' => ['width' => 1200, 'height' => 800]
                ];
                
                $paths = $this->imageService->uploadImage($file, 'gallery', $customSizes);
                $uploadedImages[] = $paths;
                
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to upload image: ' . $e->getMessage()
                ], 500);
            }
        }

        return response()->json([
            'success' => true,
            'message' => count($uploadedImages) . ' images uploaded successfully',
            'images' => $uploadedImages
        ]);
    }

    /**
     * Example: Get image URLs with WebP support
     */
    public function getImageUrls(Request $request)
    {
        $imagePath = $request->input('path');
        
        if (!$imagePath) {
            return response()->json([
                'success' => false,
                'message' => 'Image path is required'
            ], 400);
        }

        // Get image URLs
        $imageUrls = $this->imageService->getImageUrl($imagePath);
        $thumbnailUrls = $this->imageService->getThumbnailUrl($imagePath);

        return response()->json([
            'success' => true,
            'image_urls' => $imageUrls,
            'thumbnail_urls' => $thumbnailUrls
        ]);
    }

    /**
     * Example: Delete image and all variants
     */
    public function deleteImage(Request $request)
    {
        $imagePath = $request->input('path');
        
        if (!$imagePath) {
            return response()->json([
                'success' => false,
                'message' => 'Image path is required'
            ], 400);
        }

        try {
            $deleted = $this->imageService->deleteImage($imagePath);
            
            return response()->json([
                'success' => $deleted,
                'message' => $deleted ? 'Image deleted successfully' : 'Failed to delete image'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Example: Display upload form
     */
    public function showUploadForm()
    {
        return view('examples.image-upload');
    }
}