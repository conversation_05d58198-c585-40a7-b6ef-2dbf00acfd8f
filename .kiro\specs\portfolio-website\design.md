# Design Document

## Overview

The portfolio website will be built as a Laravel 11+ application with Blade templating and Tailwind CSS 4+ for styling. The architecture follows <PERSON><PERSON>'s MVC pattern with additional layers for services and repositories to maintain clean separation of concerns. The application will feature both public-facing pages for visitors and an admin interface for content management.

The design emphasizes performance, SEO optimization, and responsive design to ensure optimal user experience across all devices while maintaining professional presentation of <PERSON>'s work and capabilities.

## Architecture

### Application Structure

```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Web/
│   │   │   ├── HomeController.php
│   │   │   ├── AboutController.php
│   │   │   ├── ProjectController.php
│   │   │   ├── BlogController.php
│   │   │   ├── ContactController.php
│   │   │   ├── ServiceController.php
│   │   │   └── TestimonialController.php
│   │   └── Admin/
│   │       ├── DashboardController.php
│   │       ├── ProjectController.php
│   │       ├── BlogController.php
│   │       ├── ServiceController.php
│   │       └── ContentController.php
│   ├── Requests/
│   │   ├── ContactFormRequest.php
│   │   └── Admin/
│   │       ├── ProjectRequest.php
│   │       ├── BlogRequest.php
│   │       └── ServiceRequest.php
│   └── Middleware/
│       └── AdminAuth.php
├── Models/
│   ├── Project.php
│   ├── Blog.php
│   ├── Service.php
│   ├── Testimonial.php
│   ├── Contact.php
│   └── Content.php
├── Services/
│   ├── ProjectService.php
│   ├── BlogService.php
│   ├── ContactService.php
│   └── SEOService.php
└── Repositories/
    ├── ProjectRepository.php
    ├── BlogRepository.php
    └── ContentRepository.php
```

### Frontend Structure

```
resources/
├── views/
│   ├── layouts/
│   │   ├── app.blade.php
│   │   └── admin.blade.php
│   ├── pages/
│   │   ├── home.blade.php
│   │   ├── about.blade.php
│   │   ├── projects/
│   │   │   ├── index.blade.php
│   │   │   └── show.blade.php
│   │   ├── blog/
│   │   │   ├── index.blade.php
│   │   │   └── show.blade.php
│   │   ├── contact.blade.php
│   │   ├── services.blade.php
│   │   └── testimonials.blade.php
│   ├── admin/
│   │   ├── dashboard.blade.php
│   │   ├── projects/
│   │   ├── blog/
│   │   └── content/
│   ├── components/
│   │   ├── navbar.blade.php
│   │   ├── footer.blade.php
│   │   ├── project-card.blade.php
│   │   ├── blog-card.blade.php
│   │   └── testimonial-card.blade.php
│   └── partials/
│       ├── hero.blade.php
│       ├── stats.blade.php
│       └── seo-meta.blade.php
├── css/
│   └── app.css
└── js/
    └── app.js
```

## Components and Interfaces

### Core Models

#### Project Model
```php
class Project extends Model
{
    protected $fillable = [
        'title', 'slug', 'description', 'content', 'thumbnail', 
        'images', 'tags', 'technologies', 'project_url', 
        'github_url', 'is_featured', 'sort_order', 'status'
    ];
    
    protected $casts = [
        'images' => 'array',
        'tags' => 'array',
        'technologies' => 'array',
        'is_featured' => 'boolean'
    ];
}
```

#### Blog Model
```php
class Blog extends Model
{
    protected $fillable = [
        'title', 'slug', 'excerpt', 'content', 'thumbnail',
        'meta_title', 'meta_description', 'tags', 'status',
        'published_at'
    ];
    
    protected $casts = [
        'tags' => 'array',
        'published_at' => 'datetime'
    ];
}
```

#### Service Model
```php
class Service extends Model
{
    protected $fillable = [
        'title', 'description', 'icon', 'features', 
        'sort_order', 'is_active'
    ];
    
    protected $casts = [
        'features' => 'array',
        'is_active' => 'boolean'
    ];
}
```

### Service Layer Architecture

#### ProjectService
- Handles business logic for project operations
- Manages featured project selection
- Handles image processing and optimization
- Implements project filtering and search

#### BlogService
- Manages blog post operations
- Handles markdown/rich text processing
- Implements SEO optimization
- Manages publication scheduling

#### ContactService
- Processes contact form submissions
- Handles email notifications
- Implements spam protection
- Manages contact data storage

#### SEOService
- Generates dynamic meta tags
- Creates Open Graph data
- Manages structured data
- Handles sitemap generation

### Repository Pattern

#### ProjectRepository
```php
interface ProjectRepositoryInterface
{
    public function getFeatured(int $limit = 6): Collection;
    public function getByTag(string $tag): Collection;
    public function getPublished(): Collection;
    public function findBySlug(string $slug): ?Project;
}
```

## Data Models

### Database Schema

#### Projects Table
```sql
CREATE TABLE projects (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    content LONGTEXT,
    thumbnail VARCHAR(255),
    images JSON,
    tags JSON,
    technologies JSON,
    project_url VARCHAR(255),
    github_url VARCHAR(255),
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    status ENUM('draft', 'published') DEFAULT 'draft',
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_sort (sort_order)
);
```

#### Blog Table
```sql
CREATE TABLE blogs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    excerpt TEXT,
    content LONGTEXT,
    thumbnail VARCHAR(255),
    meta_title VARCHAR(255),
    meta_description TEXT,
    tags JSON,
    status ENUM('draft', 'published') DEFAULT 'draft',
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_published (published_at)
);
```

#### Services Table
```sql
CREATE TABLE services (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(255),
    features JSON,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);
```

#### Testimonials Table
```sql
CREATE TABLE testimonials (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(255),
    company VARCHAR(255),
    content TEXT NOT NULL,
    avatar VARCHAR(255),
    rating INT DEFAULT 5,
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_featured (is_featured)
);
```

#### Contacts Table
```sql
CREATE TABLE contacts (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied') DEFAULT 'new',
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_status (status)
);
```

#### Content Table (for dynamic content management)
```sql
CREATE TABLE contents (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    key VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(255),
    content LONGTEXT,
    meta JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## Error Handling

### Exception Handling Strategy

1. **Custom Exception Classes**
   - `ProjectNotFoundException`
   - `BlogNotFoundException`
   - `ContactFormException`
   - `AdminAuthException`

2. **Global Exception Handler**
   - Custom 404 pages for missing projects/blogs
   - Graceful degradation for missing images
   - User-friendly error messages
   - Admin notification for critical errors

3. **Validation Handling**
   - Form request validation with custom messages
   - Client-side validation with JavaScript
   - Image upload validation and processing
   - CSRF protection on all forms

### Error Response Patterns

```php
// For web routes
public function show(string $slug)
{
    $project = $this->projectService->findBySlug($slug);
    
    if (!$project) {
        abort(404, 'Project not found');
    }
    
    return view('pages.projects.show', compact('project'));
}

// For admin routes
public function store(ProjectRequest $request)
{
    try {
        $project = $this->projectService->create($request->validated());
        return redirect()->route('admin.projects.index')
            ->with('success', 'Project created successfully');
    } catch (Exception $e) {
        return back()->withInput()
            ->with('error', 'Failed to create project');
    }
}
```

## Testing Strategy

### Unit Testing
- Model relationships and scopes
- Service layer business logic
- Repository implementations
- Helper functions and utilities

### Feature Testing
- Public page rendering and content
- Admin CRUD operations
- Contact form submission
- Authentication flows
- File upload functionality

### Browser Testing
- Responsive design across devices
- JavaScript interactions
- Form validations
- Image lazy loading
- SEO meta tag generation

### Performance Testing
- Page load times
- Database query optimization
- Image optimization
- Caching effectiveness

### Test Structure
```php
tests/
├── Feature/
│   ├── Web/
│   │   ├── HomePageTest.php
│   │   ├── ProjectsPageTest.php
│   │   ├── BlogPageTest.php
│   │   └── ContactFormTest.php
│   └── Admin/
│       ├── ProjectManagementTest.php
│       ├── BlogManagementTest.php
│       └── ContentManagementTest.php
└── Unit/
    ├── Models/
    ├── Services/
    └── Repositories/
```

## Design System & UI Components

### Tailwind CSS 4+ Configuration

```javascript
// tailwind.config.js
export default {
    content: [
        './resources/**/*.blade.php',
        './resources/**/*.js',
    ],
    theme: {
        extend: {
            colors: {
                primary: {
                    50: '#eff6ff',
                    500: '#3b82f6',
                    600: '#2563eb',
                    700: '#1d4ed8',
                    900: '#1e3a8a',
                },
                secondary: {
                    50: '#f8fafc',
                    500: '#64748b',
                    600: '#475569',
                    700: '#334155',
                    900: '#0f172a',
                }
            },
            fontFamily: {
                sans: ['Inter', 'system-ui', 'sans-serif'],
                display: ['Poppins', 'system-ui', 'sans-serif'],
            },
            animation: {
                'fade-in': 'fadeIn 0.5s ease-in-out',
                'slide-up': 'slideUp 0.6s ease-out',
            }
        },
    },
    plugins: [
        require('@tailwindcss/typography'),
        require('@tailwindcss/forms'),
    ],
}
```

### Component Design Patterns

#### Reusable Blade Components
- `<x-project-card>` - Project display component
- `<x-blog-card>` - Blog post preview component
- `<x-testimonial-card>` - Testimonial display component
- `<x-service-card>` - Service offering component
- `<x-modal>` - Reusable modal component
- `<x-form-input>` - Styled form input component

#### Layout Components
- Responsive navigation with mobile menu
- Sticky header with smooth scrolling
- Footer with social links and contact info
- Breadcrumb navigation for admin pages

### Performance Optimizations

1. **Image Optimization**
   - WebP format support with fallbacks
   - Responsive image sizing
   - Lazy loading implementation
   - Image compression pipeline

2. **CSS/JS Optimization**
   - Vite build optimization
   - CSS purging with Tailwind
   - JavaScript code splitting
   - Asset versioning and caching

3. **Database Optimization**
   - Eager loading for relationships
   - Database indexing strategy
   - Query optimization
   - Caching layer implementation

4. **SEO Optimization**
   - Dynamic meta tag generation
   - Open Graph implementation
   - Structured data markup
   - XML sitemap generation
   - Robots.txt configuration