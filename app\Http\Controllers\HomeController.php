<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Blog;
use App\Models\Testimonial;
use App\Models\Content;
use App\Services\SEOService;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    protected $seoService;

    public function __construct(SEOService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display the homepage with featured content
     */
    public function index()
    {
        // Fetch featured projects (max 6)
        $featuredProjects = Project::featuredPublished(6)->get();
        
        // Fetch recent blog posts (max 3 for homepage)
        $recentBlogs = Blog::recent(3)->get();
        
        // Fetch featured testimonials (max 3 for homepage preview)
        $featuredTestimonials = Testimonial::featured()
            ->ordered()
            ->limit(3)
            ->get();
        
        // Get project stats
        $projectStats = [
            'total_projects' => Project::published()->count(),
            'years_experience' => 5, // This could be dynamic based on first project date
            'client_satisfaction' => 100, // This could be calculated from testimonials
        ];

        // Get hero content from database
        $heroContent = Content::where('key', 'hero')->first();
        $defaultHero = [
            'title' => 'Hi, I\'m Aziz Khan',
            'content' => 'I create digital experiences that blend creativity with functionality, specializing in Laravel development, 3D modeling, and UI/UX design.',
            'meta' => [
                'subtitle' => '2D/3D Artist & Web Developer',
                'cta_text' => 'View Projects',
                'cta_url' => route('projects.index')
            ]
        ];
        
        // Get footer content from database
        $footerContent = Content::where('key', 'footer')->first();
        $defaultFooter = [
            'title' => 'Aziz Khan',
            'content' => '2D/3D Artist & Web Developer passionate about creating digital experiences that blend creativity with functionality. Specializing in Laravel development, 3D modeling, and UI/UX design.',
            'meta' => [
                'copyright' => '© ' . date('Y') . ' Aziz Khan. All rights reserved.',
                'email' => '<EMAIL>'
            ]
        ];

        // Generate SEO meta tags
        $seoData = $this->seoService->generateMetaTags('home');

        return view('pages.home', compact(
            'featuredProjects',
            'recentBlogs', 
            'featuredTestimonials',
            'projectStats',
            'seoData'
        ))->with([
            'hero' => $heroContent ? $heroContent->toArray() : $defaultHero,
            'footer' => $footerContent ? $footerContent->toArray() : $defaultFooter
        ]);
    }
}