

<?php $__env->startSection('title', 'Content Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-8">
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">Content Management</h1>
    </div>

    <!-- Organized Settings Menu -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-6">
        <!-- General Settings -->
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">General Settings</h2>
                <p class="text-sm text-gray-600">Hero Section & Footer Content</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Hero Section</span>
                        <span class="text-xs text-gray-500">Homepage banner</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Footer Content</span>
                        <span class="text-xs text-gray-500">Site footer</span>
                    </div>
                </div>
                <div class="mt-6">
                    <a href="<?php echo e(route('admin.content.general-settings')); ?>" 
                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Manage General Settings
                    </a>
                </div>
            </div>
        </div>

        <!-- Site Branding Settings -->
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Site Branding</h2>
                <p class="text-sm text-gray-600">Logo, Favicon & Site Title</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Site Logo</span>
                        <span class="text-xs text-gray-500">Navbar branding</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Favicon</span>
                        <span class="text-xs text-gray-500">Browser tab icon</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Site Title</span>
                        <span class="text-xs text-gray-500">Page titles & SEO</span>
                    </div>
                </div>
                <div class="mt-6">
                    <a href="<?php echo e(route('admin.content.branding-settings')); ?>" 
                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                        Manage Branding Settings
                    </a>
                </div>
            </div>
        </div>

        <!-- About Page Settings -->
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">About Page Settings</h2>
                <p class="text-sm text-gray-600">Profile, Experience & Resume</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Profile & Summary</span>
                        <span class="text-xs text-gray-500">Bio & intro</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Work Experience</span>
                        <span class="text-xs text-gray-500">Career timeline</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Resume & Social</span>
                        <span class="text-xs text-gray-500">Downloads & links</span>
                    </div>
                </div>
                <div class="mt-6">
                    <a href="<?php echo e(route('admin.content.about-settings')); ?>" 
                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                        Manage About Settings
                    </a>
                </div>
            </div>
        </div>

        <!-- Contact Settings -->
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Contact Settings</h2>
                <p class="text-sm text-gray-600">Contact Info & reCAPTCHA</p>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Contact Information</span>
                        <span class="text-xs text-gray-500">Email & location</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Social Links</span>
                        <span class="text-xs text-gray-500">Connect with me</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">reCAPTCHA</span>
                        <span class="text-xs text-gray-500">Spam protection</span>
                    </div>
                </div>
                <div class="mt-6">
                    <a href="<?php echo e(route('admin.content.contact-settings')); ?>" 
                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                        Manage Contact Settings
                    </a>
                </div>
            </div>
        </div>
    </div>



    <!-- Testimonials Management -->
    <div class="bg-white shadow-sm rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <div>
                <h2 class="text-lg font-semibold text-gray-900">Testimonials</h2>
                <p class="text-sm text-gray-600">Manage client and colleague testimonials</p>
            </div>
            <a href="<?php echo e(route('admin.content.testimonials.create')); ?>" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                Add Testimonial
            </a>
        </div>
        <div class="p-6">
            <?php if($testimonials->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $testimonials->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center space-x-4">
                                <?php if($testimonial->avatar): ?>
                                    <img src="<?php echo e(asset('storage/' . $testimonial->avatar)); ?>" 
                                         alt="<?php echo e($testimonial->name); ?>" 
                                         class="h-10 w-10 rounded-full object-cover">
                                <?php else: ?>
                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">
                                            <?php echo e(substr($testimonial->name, 0, 1)); ?>

                                        </span>
                                    </div>
                                <?php endif; ?>
                                <div>
                                    <h4 class="font-medium text-gray-900"><?php echo e($testimonial->name); ?></h4>
                                    <p class="text-sm text-gray-600">
                                        <?php echo e($testimonial->role); ?>

                                        <?php if($testimonial->company): ?>
                                            at <?php echo e($testimonial->company); ?>

                                        <?php endif; ?>
                                    </p>
                                    <div class="flex items-center mt-1">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <svg class="h-4 w-4 <?php echo e($i <= $testimonial->rating ? 'text-yellow-400' : 'text-gray-300'); ?>" 
                                                 fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                        <?php endfor; ?>
                                        <?php if($testimonial->is_featured): ?>
                                            <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                Featured
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <a href="<?php echo e(route('admin.content.testimonials.edit', $testimonial)); ?>" 
                                   class="text-blue-600 hover:text-blue-900 text-sm font-medium">Edit</a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <div class="mt-4 text-center">
                    <a href="<?php echo e(route('admin.content.testimonials')); ?>" 
                       class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                        View All Testimonials (<?php echo e($testimonials->count()); ?>)
                    </a>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <p class="text-gray-500">No testimonials yet.</p>
                    <a href="<?php echo e(route('admin.content.testimonials.create')); ?>" 
                       class="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Add First Testimonial
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Contact Submissions -->
    <div class="bg-white shadow-sm rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Recent Contact Submissions</h2>
            <p class="text-sm text-gray-600">Latest messages from the contact form</p>
        </div>
        <div class="p-6">
            <?php if($contacts->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $contacts->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium text-gray-900"><?php echo e($contact->name); ?></h4>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                        <?php echo e($contact->status === 'new' ? 'bg-red-100 text-red-800' : 
                                           ($contact->status === 'read' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800')); ?>">
                                        <?php echo e(ucfirst($contact->status)); ?>

                                    </span>
                                </div>
                                <p class="text-sm text-gray-600"><?php echo e($contact->email); ?></p>
                                <?php if($contact->subject): ?>
                                    <p class="text-sm text-gray-800 font-medium"><?php echo e($contact->subject); ?></p>
                                <?php endif; ?>
                                <p class="text-sm text-gray-600 mt-1"><?php echo e(Str::limit($contact->message, 100)); ?></p>
                                <p class="text-xs text-gray-500 mt-2"><?php echo e($contact->created_at->format('M d, Y \a\t g:i A')); ?></p>
                            </div>
                            <div class="ml-4">
                                <a href="<?php echo e(route('admin.content.contacts.show', $contact)); ?>" 
                                   class="text-blue-600 hover:text-blue-900 text-sm font-medium">View</a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <div class="mt-4 text-center">
                    <a href="<?php echo e(route('admin.content.contacts')); ?>" 
                       class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                        View All Contacts (<?php echo e($contacts->total()); ?>)
                    </a>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <p class="text-gray-500">No contact submissions yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new-portfolio\resources\views/admin/content/index.blade.php ENDPATH**/ ?>