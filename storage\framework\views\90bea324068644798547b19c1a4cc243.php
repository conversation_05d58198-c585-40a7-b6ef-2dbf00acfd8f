
<?php
    $seo = $seoData ?? [];
    $siteTitle = $siteBranding['title'] ?? '<PERSON>';
    $siteDescription = $siteBranding['content'] ?? 'Professional portfolio showcasing expertise in web development, design, and digital solutions.';
    $siteKeywords = $siteBranding['meta']['meta_keywords'] ?? '2D Artist, 3D Artist, Web Developer, Laravel, 3ds Max, UI/UX Design, Portfolio, Aziz Khan';
    $siteAuthor = $siteBranding['meta']['meta_author'] ?? '<PERSON>';

    // Use the site title from branding settings as-is
    $title = $seo['title'] ?? $title ?? $siteTitle;
    $description = $seo['description'] ?? $description ?? $siteDescription;
    $keywords = $seo['keywords'] ?? $keywords ?? $siteKeywords;
    $ogType = $seo['ogType'] ?? $ogType ?? 'website';
    $ogUrl = $seo['ogUrl'] ?? $ogUrl ?? url()->current();
    $ogImage = $seo['ogImage'] ?? $ogImage ?? asset('images/og-default.jpg');
    $canonical = $seo['canonical'] ?? $canonical ?? url()->current();
    $robots = $seo['robots'] ?? $robots ?? 'index, follow';
    $structuredData = $seo['structuredData'] ?? $structuredData ?? null;
?>

<title><?php echo e($title); ?></title>
<meta name="description" content="<?php echo e($description); ?>">
<meta name="keywords" content="<?php echo e($keywords); ?>">
<meta name="author" content="<?php echo e($siteAuthor); ?>">


<meta property="og:title" content="<?php echo e($title); ?>">
<meta property="og:description" content="<?php echo e($description); ?>">
<meta property="og:type" content="<?php echo e($ogType); ?>">
<meta property="og:url" content="<?php echo e($ogUrl); ?>">
<meta property="og:image" content="<?php echo e($ogImage); ?>">
<meta property="og:site_name" content="<?php echo e($siteTitle); ?> Portfolio">


<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="<?php echo e($title); ?>">
<meta name="twitter:description" content="<?php echo e($description); ?>">
<meta name="twitter:image" content="<?php echo e($ogImage); ?>">


<meta name="robots" content="<?php echo e($robots); ?>">
<link rel="canonical" href="<?php echo e($canonical); ?>">


<?php if(isset($siteBranding['meta']['favicon']) && $siteBranding['meta']['favicon']): ?>
    <link rel="icon" type="image/x-icon" href="<?php echo e($siteBranding['meta']['favicon']); ?>">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e($siteBranding['meta']['favicon']); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e($siteBranding['meta']['favicon']); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e($siteBranding['meta']['favicon']); ?>">
<?php else: ?>
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(asset('images/apple-touch-icon.png')); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(asset('images/favicon-32x32.png')); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset('images/favicon-16x16.png')); ?>">
<?php endif; ?>


<?php if($structuredData): ?>
<script type="application/ld+json">
<?php echo json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT); ?>

</script>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\new-portfolio\resources\views/partials/seo-meta.blade.php ENDPATH**/ ?>