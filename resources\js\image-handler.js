/**
 * Image Handler for Portfolio Website
 * Handles image uploads, previews, and optimization
 */

class ImageHandler {
    constructor() {
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        this.init();
    }

    init() {
        this.setupImagePreviews();
        this.setupDragAndDrop();
        this.setupImageValidation();
    }

    /**
     * Setup image preview functionality
     */
    setupImagePreviews() {
        document.querySelectorAll('input[type="file"][accept*="image"]').forEach(input => {
            input.addEventListener('change', (e) => {
                this.handleImagePreview(e.target);
            });
        });
    }

    /**
     * Handle image preview display
     */
    handleImagePreview(input) {
        const files = Array.from(input.files);
        const previewContainer = input.closest('.form-group')?.querySelector('.image-preview') ||
                                input.parentElement?.querySelector('.image-preview');

        if (!previewContainer) return;

        // Clear existing previews
        previewContainer.innerHTML = '';

        files.forEach((file, index) => {
            if (this.validateFile(file)) {
                this.createImagePreview(file, previewContainer, input.name, index);
            }
        });
    }

    /**
     * Create image preview element
     */
    createImagePreview(file, container, inputName, index) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            const previewDiv = document.createElement('div');
            previewDiv.className = 'relative inline-block m-2 border rounded-lg overflow-hidden';
            
            previewDiv.innerHTML = `
                <img src="${e.target.result}" 
                     alt="Preview" 
                     class="w-32 h-32 object-cover">
                <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                    <button type="button" 
                            class="remove-image bg-red-500 text-white rounded-full p-1 opacity-0 hover:opacity-100 transition-opacity"
                            data-index="${index}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 truncate">
                    ${file.name}
                </div>
            `;

            // Add remove functionality
            previewDiv.querySelector('.remove-image').addEventListener('click', () => {
                this.removeImagePreview(previewDiv, inputName, index);
            });

            container.appendChild(previewDiv);
        };

        reader.readAsDataURL(file);
    }

    /**
     * Remove image preview
     */
    removeImagePreview(previewDiv, inputName, index) {
        previewDiv.remove();
        
        // Update file input (this is tricky with file inputs, so we'll need to handle it differently)
        const input = document.querySelector(`input[name="${inputName}"]`);
        if (input && input.files.length > 1) {
            // For multiple files, we'd need to recreate the FileList
            // This is a limitation of HTML file inputs
            console.warn('Removing individual files from multiple selection is not fully supported');
        }
    }

    /**
     * Setup drag and drop functionality
     */
    setupDragAndDrop() {
        document.querySelectorAll('.image-drop-zone').forEach(dropZone => {
            const input = dropZone.querySelector('input[type="file"]');
            
            if (!input) return;

            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, this.preventDefaults, false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, () => {
                    dropZone.classList.add('drag-over');
                }, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, () => {
                    dropZone.classList.remove('drag-over');
                }, false);
            });

            dropZone.addEventListener('drop', (e) => {
                const files = e.dataTransfer.files;
                input.files = files;
                this.handleImagePreview(input);
            }, false);
        });
    }

    /**
     * Prevent default drag behaviors
     */
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    /**
     * Setup image validation
     */
    setupImageValidation() {
        document.querySelectorAll('input[type="file"][accept*="image"]').forEach(input => {
            input.addEventListener('change', (e) => {
                this.validateImages(e.target);
            });
        });
    }

    /**
     * Validate selected images
     */
    validateImages(input) {
        const files = Array.from(input.files);
        const errors = [];

        files.forEach((file, index) => {
            const fileErrors = this.validateFile(file);
            if (fileErrors.length > 0) {
                errors.push(`File ${index + 1} (${file.name}): ${fileErrors.join(', ')}`);
            }
        });

        // Clear previous errors
        this.clearValidationErrors(input);

        if (errors.length > 0) {
            this.showValidationErrors(input, errors);
            input.value = ''; // Clear the input
        }
    }

    /**
     * Validate individual file
     */
    validateFile(file) {
        const errors = [];

        // Check file type
        if (!this.allowedTypes.includes(file.type)) {
            errors.push('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');
        }

        // Check file size
        if (file.size > this.maxFileSize) {
            errors.push(`File size too large. Maximum size is ${this.maxFileSize / (1024 * 1024)}MB.`);
        }

        return errors;
    }

    /**
     * Show validation errors
     */
    showValidationErrors(input, errors) {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'image-validation-errors mt-2 p-3 bg-red-100 border border-red-400 text-red-700 rounded';
        
        const errorList = document.createElement('ul');
        errorList.className = 'list-disc list-inside text-sm';
        
        errors.forEach(error => {
            const li = document.createElement('li');
            li.textContent = error;
            errorList.appendChild(li);
        });

        errorContainer.appendChild(errorList);
        input.parentElement.appendChild(errorContainer);
    }

    /**
     * Clear validation errors
     */
    clearValidationErrors(input) {
        const existingErrors = input.parentElement.querySelectorAll('.image-validation-errors');
        existingErrors.forEach(error => error.remove());
    }

    /**
     * Compress image before upload (optional enhancement)
     */
    async compressImage(file, maxWidth = 1920, maxHeight = 1080, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate new dimensions
                let { width, height } = img;
                
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }

                canvas.width = width;
                canvas.height = height;

                // Draw and compress
                ctx.drawImage(img, 0, 0, width, height);
                
                canvas.toBlob(resolve, file.type, quality);
            };

            img.src = URL.createObjectURL(file);
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ImageHandler();
});

// Export for use in other modules
export default ImageHandler;