<?php

namespace Database\Seeders;

use App\Models\Contact;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create specific realistic contact submissions
        $realisticContacts = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'subject' => 'Web Development Project Inquiry',
                'message' => '<PERSON> <PERSON>, I came across your portfolio and I\'m impressed with your Laravel and Vue.js work. We\'re a tech startup looking to build a SaaS platform for project management. Would you be available for a consultation call next week to discuss the project requirements and timeline?',
                'status' => 'new',
                'created_at' => now()->subDays(1)
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'subject' => '3D Visualization for Architecture Project',
                'message' => 'Hello Aziz, we\'re an architecture firm working on a luxury residential complex. We need high-quality 3D renderings and walkthrough animations for our client presentation. Your architectural visualization work is exactly what we\'re looking for. Can we schedule a meeting to discuss the project details and budget?',
                'status' => 'new',
                'created_at' => now()->subDays(2)
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'subject' => 'Character Modeling for Mobile Game',
                'message' => 'Hi there! I\'m a game developer working on a fantasy RPG for mobile platforms. I need 5 unique character models with full rigging and basic animations. Your 3D character work in your portfolio looks fantastic. What would be your timeline and pricing for this type of project?',
                'status' => 'read',
                'created_at' => now()->subDays(3)
            ],
            [
                'name' => 'Sarah Kim',
                'email' => '<EMAIL>',
                'subject' => 'E-commerce Platform Development',
                'message' => 'Dear Aziz, we\'re looking to rebuild our e-commerce platform using modern technologies. Your Laravel e-commerce project caught our attention. We need features like multi-vendor support, advanced inventory management, and payment gateway integration. Would you be interested in discussing this project?',
                'status' => 'read',
                'created_at' => now()->subDays(5)
            ],
            [
                'name' => 'David Wilson',
                'email' => '<EMAIL>',
                'subject' => 'Product Visualization Services',
                'message' => 'Hello Aziz, we\'re a product design company that needs high-quality 3D renders for our consumer electronics line. We have 8 products that need professional visualization for marketing materials. Your product rendering work is impressive. Can we discuss pricing and turnaround time?',
                'status' => 'replied',
                'created_at' => now()->subDays(7)
            ]
        ];

        foreach ($realisticContacts as $contact) {
            Contact::create($contact);
        }

        // Create additional contacts with various statuses
        Contact::factory()->count(8)->create(['status' => 'new']);
        Contact::factory()->count(6)->create(['status' => 'read']);
        Contact::factory()->count(4)->create(['status' => 'replied']);
    }
}
