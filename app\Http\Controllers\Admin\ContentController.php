<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Content;
use App\Models\Testimonial;
use App\Models\Contact;
use Illuminate\Http\Request;

class ContentController extends Controller
{
    public function index()
    {
        $contents = Content::orderBy('key')->get();
        $testimonials = Testimonial::orderBy('sort_order')->get();
        $contacts = Contact::orderBy('created_at', 'desc')->paginate(10);

        return view('admin.content.index', compact('contents', 'testimonials', 'contacts'));
    }

    public function editContent($key)
    {
        $content = Content::where('key', $key)->first();

        if (!$content) {
            $content = new Content(['key' => $key]);
        }

        // Use enhanced view for About Me content sections
        $aboutSections = [
            'about_profile_summary',
            'about_skills',
            'about_experience',
            'about_resume',
            'about_social_links',
            'about_cta'
        ];

        if (in_array($key, $aboutSections)) {
            return view('admin.content.edit-enhanced', compact('content'));
        }

        return view('admin.content.edit', compact('content'));
    }

    // New organized settings pages
    public function generalSettings()
    {
        $generalSections = [
            'hero' => 'Hero Section',
            'footer' => 'Footer Content'
        ];

        $contents = Content::whereIn('key', array_keys($generalSections))->get()->keyBy('key');

        return view('admin.content.general-settings', compact('generalSections', 'contents'));
    }



    public function brandingSettings()
    {
        $brandingSections = [
            'site_branding' => 'Site Branding & Identity'
        ];

        $contents = Content::whereIn('key', array_keys($brandingSections))->get()->keyBy('key');

        return view('admin.content.branding-settings', compact('brandingSections', 'contents'));
    }

    public function contactSettings()
    {
        $contactSections = [
            'contact_info' => 'Contact Information',
            'contact_social' => 'Connect With Me',
            'contact_recaptcha' => 'Google reCAPTCHA Settings'
        ];

        $contents = Content::whereIn('key', array_keys($contactSections))->get()->keyBy('key');

        return view('admin.content.contact-settings', compact('contactSections', 'contents'));
    }

    public function aboutSettings()
    {
        $aboutSections = [
            'about_profile_summary' => 'Profile Summary',
            'about_skills' => 'Skills & Technologies',
            'about_experience' => 'Work Experience',
            'about_resume' => 'Resume Settings',
            'about_social_links' => 'Social Media Links',
            'about_cta' => 'Call to Action Section'
        ];

        $contents = Content::whereIn('key', array_keys($aboutSections))->get()->keyBy('key');

        return view('admin.content.about-settings', compact('aboutSections', 'contents'));
    }

    public function bulkUpdateGeneral(Request $request)
    {
        $validated = $request->validate([
            'sections' => 'required|array',
            'sections.*' => 'required|array',
            'sections.*.title' => 'nullable|string|max:255',
            'sections.*.content' => 'nullable|string',
            'sections.*.meta' => 'nullable|array',
        ]);

        foreach ($validated['sections'] as $key => $data) {
            // Get existing content to preserve meta data
            $existingContent = Content::where('key', $key)->first();

            // Preserve existing meta fields if they exist
            if ($existingContent && isset($existingContent->meta)) {
                if (!isset($data['meta'])) {
                    $data['meta'] = [];
                }

                // Preserve existing meta fields that aren't being updated
                foreach ($existingContent->meta as $metaKey => $metaValue) {
                    if (!isset($data['meta'][$metaKey])) {
                        $data['meta'][$metaKey] = $metaValue;
                    }
                }
            }

            Content::updateOrCreate(
                ['key' => $key],
                $data
            );
        }

        return redirect()->route('admin.content.general-settings')
            ->with('success', 'General settings updated successfully.');
    }

    public function bulkUpdateBranding(Request $request)
    {
        try {
            $validated = $request->validate([
                'sections' => 'nullable|array',
                'sections.*' => 'nullable|array',
                'sections.*.title' => 'nullable|string|max:255',
                'sections.*.content' => 'nullable|string',
                'sections.*.meta' => 'nullable|array',
                'logo_file' => 'nullable|file|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
                'favicon_file' => 'nullable|file|mimes:ico,png|max:1024',
            ]);

            // Initialize sections array if not present
            if (!isset($validated['sections'])) {
                $validated['sections'] = [];
            }

            // Handle logo upload
            if ($request->hasFile('logo_file')) {
                $logoFile = $request->file('logo_file');
                $path = $logoFile->store('branding', 'public');

                // Ensure the site_branding section exists and has meta array
                if (!isset($validated['sections']['site_branding'])) {
                    $validated['sections']['site_branding'] = [];
                }
                if (!isset($validated['sections']['site_branding']['meta'])) {
                    $validated['sections']['site_branding']['meta'] = [];
                }

                $validated['sections']['site_branding']['meta']['logo'] = '/storage/' . $path;
            }

            // Handle favicon upload
            if ($request->hasFile('favicon_file')) {
                $faviconFile = $request->file('favicon_file');
                $path = $faviconFile->store('branding', 'public');

                // Ensure the site_branding section exists and has meta array
                if (!isset($validated['sections']['site_branding'])) {
                    $validated['sections']['site_branding'] = [];
                }
                if (!isset($validated['sections']['site_branding']['meta'])) {
                    $validated['sections']['site_branding']['meta'] = [];
                }

                $validated['sections']['site_branding']['meta']['favicon'] = '/storage/' . $path;
            }

            // Ensure site_branding section exists even if no form data was submitted for it
            if (!isset($validated['sections']['site_branding'])) {
                $validated['sections']['site_branding'] = [];
            }

            foreach ($validated['sections'] as $key => $data) {
                // Get existing content to preserve file paths
                $existingContent = Content::where('key', $key)->first();

                // Preserve existing file paths if no new file was uploaded
                if ($existingContent && isset($existingContent->meta)) {
                    if (!isset($data['meta'])) {
                        $data['meta'] = [];
                    }

                    // Preserve logo if no new logo uploaded
                    if (isset($existingContent->meta['logo']) && !isset($data['meta']['logo'])) {
                        $data['meta']['logo'] = $existingContent->meta['logo'];
                    }

                    // Preserve favicon if no new favicon uploaded
                    if (isset($existingContent->meta['favicon']) && !isset($data['meta']['favicon'])) {
                        $data['meta']['favicon'] = $existingContent->meta['favicon'];
                    }

                    // Preserve other existing meta fields that aren't being updated
                    foreach ($existingContent->meta as $metaKey => $metaValue) {
                        if (!isset($data['meta'][$metaKey])) {
                            $data['meta'][$metaKey] = $metaValue;
                        }
                    }
                }

                Content::updateOrCreate(
                    ['key' => $key],
                    $data
                );
            }

            return redirect()->route('admin.content.branding-settings')
                ->with('success', 'Site branding updated successfully.');
        } catch (\Exception $e) {
            return redirect()->route('admin.content.branding-settings')
                ->with('error', 'Failed to update site branding: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function bulkUpdateAbout(Request $request)
    {
        $validated = $request->validate([
            'sections' => 'required|array',
            'sections.*' => 'required|array',
            'sections.*.title' => 'nullable|string|max:255',
            'sections.*.content' => 'nullable|string',
            'sections.*.meta' => 'nullable|array',
            'files' => 'nullable|array',
            'files.*' => 'nullable|file|mimes:jpeg,png,jpg,gif,pdf,doc,docx|max:10240',
        ]);

        // Handle file uploads
        if ($request->hasFile('files')) {
            foreach ($request->file('files') as $key => $file) {
                if ($file) {
                    $path = $file->store('content', 'public');
                    if (!isset($validated['sections'][$key]['meta'])) {
                        $validated['sections'][$key]['meta'] = [];
                    }

                    // Determine the field name based on the section
                    if ($key === 'about_profile_summary') {
                        $validated['sections'][$key]['meta']['image'] = '/storage/' . $path;
                    } elseif ($key === 'about_resume') {
                        $validated['sections'][$key]['meta']['resume_path'] = $path;
                    }
                }
            }
        }

        foreach ($validated['sections'] as $key => $data) {
            // Get existing content to preserve file paths
            $existingContent = Content::where('key', $key)->first();

            // Preserve existing file paths if no new file was uploaded
            if ($existingContent && isset($existingContent->meta)) {
                if (!isset($data['meta'])) {
                    $data['meta'] = [];
                }

                // Preserve profile image if no new image uploaded
                if (
                    $key === 'about_profile_summary' &&
                    isset($existingContent->meta['image']) &&
                    !isset($data['meta']['image'])
                ) {
                    $data['meta']['image'] = $existingContent->meta['image'];
                }

                // Preserve resume file if no new file uploaded
                if (
                    $key === 'about_resume' &&
                    isset($existingContent->meta['resume_path']) &&
                    !isset($data['meta']['resume_path'])
                ) {
                    $data['meta']['resume_path'] = $existingContent->meta['resume_path'];
                }

                // Preserve other existing meta fields that aren't being updated
                foreach ($existingContent->meta as $metaKey => $metaValue) {
                    if (!isset($data['meta'][$metaKey])) {
                        $data['meta'][$metaKey] = $metaValue;
                    }
                }
            }

            // Handle about_experience timeline data
            if ($key === 'about_experience' && isset($data['meta']['timeline'])) {
                foreach ($data['meta']['timeline'] as $index => $experience) {
                    // Convert achievements from textarea to array
                    if (isset($experience['achievements']) && is_string($experience['achievements'])) {
                        $achievements = explode("\n", $experience['achievements']);
                        $achievements = array_map('trim', $achievements);
                        $achievements = array_filter($achievements, function ($achievement) {
                            return !empty($achievement);
                        });
                        $data['meta']['timeline'][$index]['achievements'] = array_values($achievements);
                    }

                    // Convert technologies from comma-separated to array
                    if (isset($experience['technologies']) && is_string($experience['technologies'])) {
                        $technologies = explode(',', $experience['technologies']);
                        $technologies = array_map('trim', $technologies);
                        $technologies = array_filter($technologies, function ($tech) {
                            return !empty($tech);
                        });
                        $data['meta']['timeline'][$index]['technologies'] = array_values($technologies);
                    }
                }
            }

            Content::updateOrCreate(
                ['key' => $key],
                $data
            );
        }

        return redirect()->route('admin.content.about-settings')
            ->with('success', 'About page settings updated successfully.');
    }

    public function bulkUpdateContact(Request $request)
    {
        $validated = $request->validate([
            'sections' => 'required|array',
            'sections.*' => 'required|array',
            'sections.*.title' => 'nullable|string|max:255',
            'sections.*.content' => 'nullable|string',
            'sections.*.meta' => 'nullable|array',
        ]);

        foreach ($validated['sections'] as $key => $data) {
            // Get existing content to preserve meta data
            $existingContent = Content::where('key', $key)->first();

            // Preserve existing meta fields if they exist
            if ($existingContent && isset($existingContent->meta)) {
                if (!isset($data['meta'])) {
                    $data['meta'] = [];
                }

                // Preserve existing meta fields that aren't being updated
                foreach ($existingContent->meta as $metaKey => $metaValue) {
                    if (!isset($data['meta'][$metaKey])) {
                        $data['meta'][$metaKey] = $metaValue;
                    }
                }
            }

            Content::updateOrCreate(
                ['key' => $key],
                $data
            );
        }

        return redirect()->route('admin.content.contact-settings')
            ->with('success', 'Contact settings updated successfully.');
    }

    public function updateContent(Request $request, $key)
    {
        $validated = $request->validate([
            'title' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'meta' => 'nullable|array',
        ]);

        $validated['key'] = $key;

        // Get existing content to preserve file paths and other meta data
        $existingContent = Content::where('key', $key)->first();

        // Preserve existing meta fields if they exist
        if ($existingContent && isset($existingContent->meta)) {
            if (!isset($validated['meta'])) {
                $validated['meta'] = [];
            }

            // Preserve existing meta fields that aren't being updated
            foreach ($existingContent->meta as $metaKey => $metaValue) {
                if (!isset($validated['meta'][$metaKey])) {
                    $validated['meta'][$metaKey] = $metaValue;
                }
            }
        }

        // Handle skills meta field - convert from textarea to array
        if ($key === 'skills' && isset($validated['meta']['skills'])) {
            $skills = explode("\n", $validated['meta']['skills']);
            $skills = array_map('trim', $skills);
            $skills = array_filter($skills, function ($skill) {
                return !empty($skill);
            });
            $validated['meta']['skills'] = array_values($skills);
        }

        // Handle about_experience timeline data
        if ($key === 'about_experience' && isset($validated['meta']['timeline'])) {
            foreach ($validated['meta']['timeline'] as $index => $experience) {
                // Convert achievements from textarea to array
                if (isset($experience['achievements']) && is_string($experience['achievements'])) {
                    $achievements = explode("\n", $experience['achievements']);
                    $achievements = array_map('trim', $achievements);
                    $achievements = array_filter($achievements, function ($achievement) {
                        return !empty($achievement);
                    });
                    $validated['meta']['timeline'][$index]['achievements'] = array_values($achievements);
                }

                // Convert technologies from comma-separated to array
                if (isset($experience['technologies']) && is_string($experience['technologies'])) {
                    $technologies = explode(',', $experience['technologies']);
                    $technologies = array_map('trim', $technologies);
                    $technologies = array_filter($technologies, function ($tech) {
                        return !empty($tech);
                    });
                    $validated['meta']['timeline'][$index]['technologies'] = array_values($technologies);
                }
            }
        }

        Content::updateOrCreate(
            ['key' => $key],
            $validated
        );

        return redirect()->route('admin.content.index')
            ->with('success', 'Content updated successfully.');
    }

    public function testimonials()
    {
        $testimonials = Testimonial::orderBy('sort_order')->paginate(10);

        return view('admin.content.testimonials', compact('testimonials'));
    }

    public function createTestimonial()
    {
        return view('admin.content.testimonials-create');
    }

    public function storeTestimonial(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'role' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'content' => 'required|string',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'rating' => 'integer|min:1|max:5',
            'is_featured' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('testimonials', 'public');
            $validated['avatar'] = $avatarPath;
        }

        $validated['is_featured'] = $request->has('is_featured');
        $validated['rating'] = $validated['rating'] ?? 5;
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        Testimonial::create($validated);

        return redirect()->route('admin.content.testimonials')
            ->with('success', 'Testimonial created successfully.');
    }

    public function editTestimonial(Testimonial $testimonial)
    {
        return view('admin.content.testimonials-edit', compact('testimonial'));
    }

    public function updateTestimonial(Request $request, Testimonial $testimonial)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'role' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'content' => 'required|string',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'rating' => 'integer|min:1|max:5',
            'is_featured' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($testimonial->avatar && file_exists(public_path('storage/' . $testimonial->avatar))) {
                unlink(public_path('storage/' . $testimonial->avatar));
            }

            $avatarPath = $request->file('avatar')->store('testimonials', 'public');
            $validated['avatar'] = $avatarPath;
        }

        $validated['is_featured'] = $request->has('is_featured');
        $validated['rating'] = $validated['rating'] ?? $testimonial->rating;
        $validated['sort_order'] = $validated['sort_order'] ?? $testimonial->sort_order;

        $testimonial->update($validated);

        return redirect()->route('admin.content.testimonials')
            ->with('success', 'Testimonial updated successfully.');
    }

    public function destroyTestimonial(Testimonial $testimonial)
    {
        // Delete avatar file if exists
        if ($testimonial->avatar && file_exists(public_path('storage/' . $testimonial->avatar))) {
            unlink(public_path('storage/' . $testimonial->avatar));
        }

        $testimonial->delete();

        return redirect()->route('admin.content.testimonials')
            ->with('success', 'Testimonial deleted successfully.');
    }

    public function contacts()
    {
        $contacts = Contact::orderBy('created_at', 'desc')->paginate(15);

        return view('admin.content.contacts', compact('contacts'));
    }

    public function showContact(Contact $contact)
    {
        // Mark as read if it's new
        if ($contact->status === 'new') {
            $contact->update(['status' => 'read']);
        }

        return view('admin.content.contacts-show', compact('contact'));
    }

    public function updateContactStatus(Request $request, Contact $contact)
    {
        $validated = $request->validate([
            'status' => 'required|in:new,read,replied',
        ]);

        $contact->update($validated);

        return redirect()->route('admin.content.contacts')
            ->with('success', 'Contact status updated successfully.');
    }

    public function destroyContact(Contact $contact)
    {
        $contact->delete();

        return redirect()->route('admin.content.contacts')
            ->with('success', 'Contact deleted successfully.');
    }

    public function bulkContactAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,mark_read,mark_replied',
            'contacts' => 'required|array|min:1',
            'contacts.*' => 'exists:contacts,id',
        ]);

        $contactIds = $validated['contacts'];
        $action = $validated['action'];

        switch ($action) {
            case 'delete':
                $deletedCount = Contact::whereIn('id', $contactIds)->delete();
                $message = "Successfully deleted {$deletedCount} contact(s).";
                break;
            case 'mark_read':
                $updatedCount = Contact::whereIn('id', $contactIds)->update(['status' => 'read']);
                $message = "Successfully marked {$updatedCount} contact(s) as read.";
                break;
            case 'mark_replied':
                $updatedCount = Contact::whereIn('id', $contactIds)->update(['status' => 'replied']);
                $message = "Successfully marked {$updatedCount} contact(s) as replied.";
                break;
            default:
                $message = 'Invalid action selected.';
        }

        return redirect()->route('admin.content.contacts')
            ->with('success', $message);
    }
}
