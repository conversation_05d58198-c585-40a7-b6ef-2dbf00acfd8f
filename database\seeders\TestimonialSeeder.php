<?php

namespace Database\Seeders;

use App\Models\Testimonial;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create specific featured testimonials with realistic content
        $featuredTestimonials = [
            [
                'name' => '<PERSON>',
                'role' => 'CTO',
                'company' => 'TechFlow Solutions',
                'content' => '<PERSON> delivered an exceptional e-commerce platform that exceeded our expectations. His expertise in Laravel and Vue.js, combined with his attention to detail, resulted in a robust solution that handles our high-volume traffic seamlessly. The project was completed on time and within budget. I highly recommend <PERSON> for any complex web development project.',
                'avatar' => 'portfolio_30.jpg',
                'rating' => 5,
                'is_featured' => true,
                'sort_order' => 1
            ],
            [
                'name' => '<PERSON>',
                'role' => 'Creative Director',
                'company' => 'Pixel Studios',
                'content' => 'Working with <PERSON> on our 3D character design project was a fantastic experience. His technical skills in 3ds Max and ZBrush are outstanding, but what really impressed me was his creative vision and ability to bring our concepts to life. The characters he created became the cornerstone of our mobile game\'s success.',
                'avatar' => 'portfolio_31.jpg',
                'rating' => 5,
                'is_featured' => true,
                'sort_order' => 2
            ],
            [
                'name' => '<PERSON> <PERSON>',
                'role' => 'Project Manager',
                'company' => 'Digital Innovations Inc',
                'content' => 'Aziz\'s work on our real estate management system was phenomenal. He understood our complex requirements and delivered a solution that streamlined our entire workflow. His communication throughout the project was excellent, and he was always available to address our concerns. The system has increased our productivity by 40%.',
                'avatar' => 'portfolio_32.jpg',
                'rating' => 5,
                'is_featured' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'David Thompson',
                'role' => 'Lead Architect',
                'company' => 'Metropolitan Design Group',
                'content' => 'The architectural visualizations Aziz created for our luxury residential project were absolutely stunning. His attention to detail and photorealistic rendering quality helped us secure multiple high-value clients. The walkthrough animations were particularly impressive and became a key part of our sales presentations.',
                'avatar' => 'portfolio_33.jpg',
                'rating' => 5,
                'is_featured' => true,
                'sort_order' => 4
            ]
        ];

        foreach ($featuredTestimonials as $testimonial) {
            Testimonial::create($testimonial);
        }

        // Create additional realistic testimonials
        $additionalTestimonials = [
            [
                'name' => 'Jennifer Walsh',
                'role' => 'CEO',
                'company' => 'StartupHub',
                'content' => 'Aziz built our SaaS platform from the ground up. His expertise in multi-tenant architecture and scalable design patterns was exactly what we needed. The platform now serves thousands of users without any performance issues.',
                'avatar' => 'portfolio_34.jpg',
                'rating' => 5,
                'is_featured' => false,
                'sort_order' => 5
            ],
            [
                'name' => 'Robert Kim',
                'role' => 'Marketing Director',
                'company' => 'ElectroTech Corp',
                'content' => 'The product visualizations Aziz created for our consumer electronics line were exceptional. The quality was indistinguishable from professional photography, and the animations perfectly showcased our product features.',
                'avatar' => 'portfolio_35.jpg',
                'rating' => 5,
                'is_featured' => false,
                'sort_order' => 6
            ],
            [
                'name' => 'Lisa Anderson',
                'role' => 'Senior Developer',
                'company' => 'CodeCraft Solutions',
                'content' => 'I had the pleasure of working alongside Aziz on a complex Laravel project. His code quality is exceptional, and his problem-solving skills are top-notch. He\'s also a great team player who\'s always willing to help others.',
                'avatar' => 'portfolio_36.jpg',
                'rating' => 5,
                'is_featured' => false,
                'sort_order' => 7
            ],
            [
                'name' => 'Mark Stevens',
                'role' => 'Game Producer',
                'company' => 'Indie Game Studios',
                'content' => 'Aziz\'s 3D modeling and animation work was crucial to our game\'s success. His characters were not only visually appealing but also optimized perfectly for mobile platforms. The project was delivered ahead of schedule.',
                'avatar' => 'portfolio_2.jpg',
                'rating' => 5,
                'is_featured' => false,
                'sort_order' => 8
            ],
            [
                'name' => 'Amanda Foster',
                'role' => 'UX Designer',
                'company' => 'Design Forward',
                'content' => 'Collaborating with Aziz on the UI/UX aspects of our web application was seamless. He has a great eye for design and understands user experience principles. The final product was both beautiful and highly functional.',
                'avatar' => 'portfolio_3.jpg',
                'rating' => 4,
                'is_featured' => false,
                'sort_order' => 9
            ],
            [
                'name' => 'James Wilson',
                'role' => 'Technical Lead',
                'company' => 'Enterprise Solutions Ltd',
                'content' => 'Aziz\'s expertise in Laravel and modern PHP development practices helped us modernize our legacy system. His approach to refactoring was methodical and resulted in significant performance improvements.',
                'avatar' => 'portfolio_4.jpg',
                'rating' => 5,
                'is_featured' => false,
                'sort_order' => 10
            ],
            [
                'name' => 'Rachel Green',
                'role' => 'Art Director',
                'company' => 'Creative Collective',
                'content' => 'The 3D renders Aziz produced for our advertising campaign were absolutely stunning. His ability to create photorealistic materials and lighting setups is remarkable. Our client was thrilled with the results.',
                'avatar' => 'portfolio_5.jpg',
                'rating' => 5,
                'is_featured' => false,
                'sort_order' => 11
            ],
            [
                'name' => 'Thomas Brown',
                'role' => 'Startup Founder',
                'company' => 'InnovateTech',
                'content' => 'As a non-technical founder, I needed someone who could translate my vision into reality. Aziz not only built an amazing product but also educated me throughout the process. His patience and expertise were invaluable.',
                'avatar' => 'portfolio_6.jpg',
                'rating' => 5,
                'is_featured' => false,
                'sort_order' => 12
            ]
        ];

        foreach ($additionalTestimonials as $testimonial) {
            Testimonial::create($testimonial);
        }
    }
}
