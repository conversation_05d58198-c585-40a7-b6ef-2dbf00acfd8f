<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Services\SEOService;
use Illuminate\Http\Request;

class ServiceController extends Controller
{
    protected $seoService;

    public function __construct(SEOService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display a listing of active services.
     */
    public function index()
    {
        $services = Service::active()
            ->ordered()
            ->get();

        // Generate SEO meta tags
        $seoData = $this->seoService->generateMetaTags('services');

        return view('pages.services', compact('services', 'seoData'));
    }
}