<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'src' => '',
    'webp' => null,
    'alt' => '',
    'class' => '',
    'loading' => 'lazy',
    'width' => null,
    'height' => null,
    'placeholder' => 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlmYTZiNyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'src' => '',
    'webp' => null,
    'alt' => '',
    'class' => '',
    'loading' => 'lazy',
    'width' => null,
    'height' => null,
    'placeholder' => 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlmYTZiNyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<picture class="lazy-image-container <?php echo e($class); ?>">
    <?php if($webp): ?>
        <source srcset="<?php echo e($webp); ?>" type="image/webp">
    <?php endif; ?>
    
    <img 
        src="<?php echo e($placeholder); ?>"
        data-src="<?php echo e($src); ?>"
        alt="<?php echo e($alt); ?>"
        loading="<?php echo e($loading); ?>"
        class="lazy-image transition-opacity duration-300 opacity-0"
        <?php if($width): ?> width="<?php echo e($width); ?>" <?php endif; ?>
        <?php if($height): ?> height="<?php echo e($height); ?>" <?php endif; ?>
        <?php echo e($attributes->except(['src', 'webp', 'alt', 'class', 'loading', 'width', 'height', 'placeholder'])); ?>

    >
</picture>

<?php if (! $__env->hasRenderedOnce('b888e93d-c661-45e8-9fd8-4bd9deb3a06a')): $__env->markAsRenderedOnce('b888e93d-c661-45e8-9fd8-4bd9deb3a06a'); ?>
    <?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Intersection Observer for lazy loading
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const src = img.getAttribute('data-src');
                        
                        if (src) {
                            img.src = src;
                            img.classList.add('opacity-100');
                            img.classList.remove('opacity-0');
                            
                            img.onload = function() {
                                img.classList.add('loaded');
                            };
                            
                            img.onerror = function() {
                                img.classList.add('error');
                                // Fallback to a default error image if needed
                                console.warn('Failed to load image:', src);
                            };
                            
                            observer.unobserve(img);
                        }
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Observe all lazy images
            document.querySelectorAll('.lazy-image').forEach(img => {
                imageObserver.observe(img);
            });
        });
    </script>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('styles'); ?>
    <style>
        .lazy-image-container {
            position: relative;
            overflow: hidden;
        }
        
        .lazy-image {
            width: 100%;
            height: auto;
            object-fit: cover;
        }
        
        .lazy-image.loaded {
            opacity: 1;
        }
        
        .lazy-image.error {
            opacity: 0.5;
            filter: grayscale(100%);
        }
        
        /* Loading animation */
        .lazy-image-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
            z-index: 1;
        }
        
        .lazy-image.loaded + .lazy-image-container::before {
            display: none;
        }
        
        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
    <?php $__env->stopPush(); ?>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\new-portfolio\resources\views/components/lazy-image.blade.php ENDPATH**/ ?>