/* Image handling and optimization styles */

/* Lazy loading styles */
.lazy-image-container {
    position: relative;
    overflow: hidden;
    background-color: #f3f4f6;
}

.lazy-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    transition: opacity 0.3s ease-in-out;
}

.lazy-image.loaded {
    opacity: 1;
}

.lazy-image.error {
    opacity: 0.5;
    filter: grayscale(100%);
}

/* Loading shimmer effect */
.lazy-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 1.5s infinite;
    z-index: 1;
}

.lazy-image.loaded + .lazy-image-container::before,
.lazy-image-container:has(.lazy-image.loaded)::before {
    display: none;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Image upload and preview styles */
.image-drop-zone {
    border: 2px dashed #d1d5db;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.image-drop-zone:hover {
    border-color: #3b82f6;
    background-color: #f8fafc;
}

.image-drop-zone.drag-over {
    border-color: #3b82f6;
    background-color: #eff6ff;
    transform: scale(1.02);
}

.image-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.image-preview-item {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.image-preview-item img {
    width: 8rem;
    height: 8rem;
    object-fit: cover;
}

.image-preview-item .overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.image-preview-item:hover .overlay {
    background-color: rgba(0, 0, 0, 0.3);
}

.image-preview-item .remove-btn {
    background-color: #ef4444;
    color: white;
    border-radius: 50%;
    padding: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.image-preview-item:hover .remove-btn {
    opacity: 1;
}

.image-preview-item .file-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.75);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* WebP support detection */
.webp .webp-image {
    display: block;
}

.webp .fallback-image {
    display: none;
}

.no-webp .webp-image {
    display: none;
}

.no-webp .fallback-image {
    display: block;
}

/* Responsive image grid */
.image-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.image-grid-item {
    aspect-ratio: 4/3;
    overflow: hidden;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.image-grid-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-grid-item:hover img {
    transform: scale(1.05);
}

/* Image optimization indicators */
.image-format-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    text-transform: uppercase;
}

.image-format-badge.webp {
    background-color: #10b981;
}

.image-format-badge.jpg,
.image-format-badge.jpeg {
    background-color: #f59e0b;
}

.image-format-badge.png {
    background-color: #3b82f6;
}

/* Loading states */
.image-loading {
    background: linear-gradient(-90deg, #f0f0f0 0%, #e0e0e0 50%, #f0f0f0 100%);
    background-size: 400% 400%;
    animation: pulse 1.2s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: -135% 0%;
    }
}

/* Error states */
.image-error {
    background-color: #fee2e2;
    border: 1px solid #fecaca;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #dc2626;
    font-size: 0.875rem;
}

.image-error::before {
    content: '⚠️';
    margin-right: 0.5rem;
}

/* Validation error styles */
.image-validation-errors {
    background-color: #fee2e2;
    border: 1px solid #fca5a5;
    color: #dc2626;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-top: 0.5rem;
}

.image-validation-errors ul {
    list-style-type: disc;
    list-style-position: inside;
    font-size: 0.875rem;
}

.image-validation-errors li {
    margin-bottom: 0.25rem;
}

.image-validation-errors li:last-child {
    margin-bottom: 0;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
    .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.5rem;
    }
    
    .image-preview-item img {
        width: 6rem;
        height: 6rem;
    }
    
    .image-drop-zone {
        padding: 1rem;
    }
}