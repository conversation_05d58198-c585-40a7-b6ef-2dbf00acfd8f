<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Services\SEOService;
use Illuminate\Http\Request;

class ProjectController extends Controller
{
    protected $seoService;

    public function __construct(SEOService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display a listing of projects with optional filtering
     */
    public function index(Request $request)
    {
        $query = Project::published()->ordered();
        
        // Filter by technology if provided
        if ($request->has('technology') && $request->technology) {
            $query->byTechnology($request->technology);
        }
        
        // Filter by tag if provided
        if ($request->has('tag') && $request->tag) {
            $query->byTag($request->tag);
        }
        
        $projects = $query->get();
        
        // Get all unique technologies and tags for filter options
        $allProjects = Project::published()->get();
        $technologies = $allProjects->pluck('technologies')->flatten()->unique()->filter()->sort()->values();
        $tags = $allProjects->pluck('tags')->flatten()->unique()->filter()->sort()->values();
        
        // Generate SEO meta tags
        $seoData = $this->seoService->generateMetaTags('projects');
        
        return view('pages.projects.index', compact('projects', 'technologies', 'tags', 'seoData'));
    }

    /**
     * Display the specified project
     */
    public function show(Project $project)
    {
        // Ensure the project is published or return 404
        if (!$project->isPublished()) {
            abort(404);
        }
        
        // Get related projects (same technologies, excluding current project)
        $relatedProjects = collect();
        if ($project->technologies && count($project->technologies) > 0) {
            $relatedProjects = Project::published()
                ->where('id', '!=', $project->id)
                ->where(function ($query) use ($project) {
                    foreach ($project->technologies as $tech) {
                        $query->orWhereJsonContains('technologies', $tech);
                    }
                })
                ->ordered()
                ->limit(3)
                ->get();
        }
        
        // Generate SEO meta tags for this specific project
        $seoData = $this->seoService->generateMetaTags('projects', $project);
        
        return view('pages.projects.show', compact('project', 'relatedProjects', 'seoData'));
    }
}