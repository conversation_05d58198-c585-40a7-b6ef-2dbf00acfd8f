<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Upload Example</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">Image Upload Examples</h1>
            
            <!-- Single Image Upload -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Single Image Upload</h2>
                
                <form action="/examples/upload-single" method="POST" enctype="multipart/form-data">
                    @csrf
                    <x-image-upload 
                        name="image"
                        label="Upload Single Image"
                        :required="true"
                        help="Upload a single image to see the optimization in action"
                    />
                    
                    <button type="submit" class="btn-primary mt-4">
                        Upload Image
                    </button>
                </form>
            </div>

            <!-- Multiple Image Upload -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Multiple Image Upload</h2>
                
                <form action="/examples/upload-multiple" method="POST" enctype="multipart/form-data">
                    @csrf
                    <x-image-upload 
                        name="images"
                        label="Upload Multiple Images"
                        :multiple="true"
                        help="Upload multiple images to see batch processing with custom sizes"
                    />
                    
                    <button type="submit" class="btn-primary mt-4">
                        Upload Images
                    </button>
                </form>
            </div>

            <!-- Image Display Examples -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Image Display Examples</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Example with existing image -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">Optimized Image</h3>
                        <x-optimized-image 
                            path="portfolio.jpg"
                            alt="Portfolio example"
                            class="w-full h-48 object-cover rounded-lg"
                        />
                    </div>
                    
                    <!-- Example with thumbnail -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">Thumbnail Version</h3>
                        <x-optimized-image 
                            path="portfolio.jpg"
                            alt="Portfolio thumbnail"
                            class="w-full h-48 object-cover rounded-lg"
                            thumbnail="true"
                        />
                    </div>
                    
                    <!-- Example with lazy loading -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">Lazy Loaded Image</h3>
                        <x-lazy-image 
                            src="{{ asset('images/portfolio_2.jpg') }}"
                            webp="{{ asset('images/portfolio_2.webp') }}"
                            alt="Lazy loaded example"
                            class="w-full h-48 object-cover rounded-lg"
                        />
                    </div>
                </div>
            </div>

            <!-- Feature Showcase -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Features Demonstrated</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">Upload Features</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-1">
                            <li>Drag and drop support</li>
                            <li>Image preview before upload</li>
                            <li>Client-side validation</li>
                            <li>File size and type checking</li>
                            <li>Multiple file selection</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">Optimization Features</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-1">
                            <li>Automatic WebP generation</li>
                            <li>Thumbnail creation</li>
                            <li>Image compression</li>
                            <li>Lazy loading</li>
                            <li>Responsive image support</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>