<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\TestimonialController;

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');

Route::get('/about', [AboutController::class, 'index'])->name('about');
Route::get('/resume/download', [AboutController::class, 'downloadResume'])->name('resume.download');
Route::get('/debug/resume', [AboutController::class, 'debugResume'])->name('debug.resume'); // Temporary debug route
Route::get('/create-default-resume', [AboutController::class, 'createDefaultResume'])->name('create.default.resume'); // Temporary route

Route::get('/projects', [ProjectController::class, 'index'])->name('projects.index');
Route::get('/projects/{project}', [ProjectController::class, 'show'])->name('projects.show');

Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{blog}', [BlogController::class, 'show'])->name('blog.show');

Route::get('/services', [ServiceController::class, 'index'])->name('services');

Route::get('/testimonials', [TestimonialController::class, 'index'])->name('testimonials');

Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// SEO Routes
use App\Http\Controllers\SEOController;
Route::get('/sitemap.xml', [SEOController::class, 'sitemap'])->name('sitemap');
Route::get('/robots.txt', [SEOController::class, 'robots'])->name('robots');

// Admin Routes
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\ProjectController as AdminProjectController;
use App\Http\Controllers\Admin\BlogController as AdminBlogController;
use App\Http\Controllers\Admin\ServiceController as AdminServiceController;
use App\Http\Controllers\Admin\ContentController as AdminContentController;
use App\Http\Controllers\Admin\MenuController as AdminMenuController;
use App\Http\Controllers\Admin\PageController as AdminPageController;

Route::prefix('admin')->name('admin.')->group(function () {
    // Authentication routes (not protected)
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    
    // Password reset routes
    Route::get('/forgot-password', [AuthController::class, 'showForgotPassword'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'sendResetLink'])->name('password.email');
    Route::get('/reset-password/{token}', [AuthController::class, 'showResetPassword'])->name('password.reset');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');

    // Protected admin routes
    Route::middleware('admin.auth')->group(function () {
        Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.alt');
        
        // Projects management
        Route::resource('projects', AdminProjectController::class);
        Route::post('/projects/bulk-action', [AdminProjectController::class, 'bulkAction'])->name('projects.bulk-action');
        
        // Blog management
        Route::resource('blog', AdminBlogController::class);
        Route::post('/blog/bulk-action', [AdminBlogController::class, 'bulkAction'])->name('blog.bulk-action');
        
        // Services management
        Route::resource('services', AdminServiceController::class);
        Route::post('/services/bulk-action', [AdminServiceController::class, 'bulkAction'])->name('services.bulk-action');
        
        // Content management
        Route::get('/content', [AdminContentController::class, 'index'])->name('content.index');
        Route::get('/content/{key}/edit', [AdminContentController::class, 'editContent'])->name('content.edit');
        Route::put('/content/{key}', [AdminContentController::class, 'updateContent'])->name('content.update');
        
        // Organized Settings Pages
        Route::get('/content/general-settings', [AdminContentController::class, 'generalSettings'])->name('content.general-settings');
        Route::post('/content/general-bulk-update', [AdminContentController::class, 'bulkUpdateGeneral'])->name('content.general-bulk-update');
        
        Route::get('/content/branding-settings', [AdminContentController::class, 'brandingSettings'])->name('content.branding-settings');
        Route::post('/content/branding-bulk-update', [AdminContentController::class, 'bulkUpdateBranding'])->name('content.branding-bulk-update');
        
        Route::get('/content/about-settings', [AdminContentController::class, 'aboutSettings'])->name('content.about-settings');
        Route::post('/content/about-bulk-update', [AdminContentController::class, 'bulkUpdateAbout'])->name('content.about-bulk-update');
        
        Route::get('/content/contact-settings', [AdminContentController::class, 'contactSettings'])->name('content.contact-settings');
        Route::post('/content/contact-bulk-update', [AdminContentController::class, 'bulkUpdateContact'])->name('content.contact-bulk-update');
        
        // Testimonials management
        Route::get('/content/testimonials', [AdminContentController::class, 'testimonials'])->name('content.testimonials');
        Route::get('/content/testimonials/create', [AdminContentController::class, 'createTestimonial'])->name('content.testimonials.create');
        Route::post('/content/testimonials', [AdminContentController::class, 'storeTestimonial'])->name('content.testimonials.store');
        Route::get('/content/testimonials/{testimonial}/edit', [AdminContentController::class, 'editTestimonial'])->name('content.testimonials.edit');
        Route::put('/content/testimonials/{testimonial}', [AdminContentController::class, 'updateTestimonial'])->name('content.testimonials.update');
        Route::delete('/content/testimonials/{testimonial}', [AdminContentController::class, 'destroyTestimonial'])->name('content.testimonials.destroy');
        
        // Contacts management
        Route::get('/content/contacts', [AdminContentController::class, 'contacts'])->name('content.contacts');
        Route::post('/content/contacts/bulk-action', [AdminContentController::class, 'bulkContactAction'])->name('content.contacts.bulk-action');
        Route::get('/content/contacts/{contact}', [AdminContentController::class, 'showContact'])->name('content.contacts.show');
        Route::patch('/content/contacts/{contact}/status', [AdminContentController::class, 'updateContactStatus'])->name('content.contacts.update-status');
        Route::delete('/content/contacts/{contact}', [AdminContentController::class, 'destroyContact'])->name('content.contacts.destroy');
        
        // Menu management
        Route::post('/menus/bulk-action', [AdminMenuController::class, 'bulkAction'])->name('menus.bulk-action');
        Route::get('/menus/test-bulk', function() {
            return response()->json(['message' => 'Bulk action route is accessible', 'timestamp' => now()]);
        })->name('menus.test-bulk');
        Route::resource('menus', AdminMenuController::class);
        
        // Page management
        Route::resource('pages', AdminPageController::class);
    });
});

// Dynamic Pages Route (must be at the end to avoid conflicts)
use App\Http\Controllers\PageController;
Route::get('/page/{page}', [PageController::class, 'show'])->name('pages.show');
