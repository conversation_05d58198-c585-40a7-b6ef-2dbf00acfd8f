<footer class="bg-gray-900 text-white" role="contentinfo" aria-label="Site footer">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Brand Section -->
            <div class="lg:col-span-2">
                <div class="flex items-center mb-4">
                    <span class="text-xl sm:text-2xl font-bold font-display text-white">
                        @if(isset($footer['title']))
                            {{ $footer['title'] }}
                        @else
                            <PERSON> Khan
                        @endif
                    </span>
                </div>
                <p class="text-gray-300 mb-6 max-w-md text-sm sm:text-base leading-relaxed">
                    @if(isset($footer['content']))
                        {{ $footer['content'] }}
                    @else
                        2D/3D Artist & Web Developer passionate about creating digital experiences that blend creativity with functionality. Specializing in Laravel development, 3D modeling, and UI/UX design.
                    @endif
                </p>
                
                <!-- Social Media Links -->
                <div class="flex flex-wrap gap-4" role="list" aria-label="Social media links">
                    <a href="https://linkedin.com/in/aziz-khan" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="text-gray-400 hover:text-white focus:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md p-1"
                       aria-label="Visit LinkedIn profile (opens in new tab)"
                       role="listitem">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                    
                    <a href="https://github.com/aziz-khan" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="text-gray-400 hover:text-white focus:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md p-1"
                       aria-label="Visit GitHub profile (opens in new tab)"
                       role="listitem">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd"/>
                        </svg>
                    </a>
                    
                    <a href="https://artstation.com/aziz-khan" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="text-gray-400 hover:text-white focus:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md p-1"
                       aria-label="Visit ArtStation profile (opens in new tab)"
                       role="listitem">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path d="M0 17.723l2.027 3.505h.001a2.424 2.424 0 0 0 2.164 1.333h13.457l-2.792-4.838H0zm24 .025c0-.484-.143-.935-.388-1.314L15.728 2.728a2.424 2.424 0 0 0-2.142-1.289H9.419L21.598 22.54l1.92-3.325c.378-.65.482-1.291.482-1.467zM2.043 6.578L10.27 21.314h4.134L6.178 6.578H2.043z"/>
                        </svg>
                    </a>
                    
                    <a href="mailto:<EMAIL>" 
                       class="text-gray-400 hover:text-white focus:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md p-1"
                       aria-label="Send <NAME_EMAIL>"
                       role="listitem">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                    </a>
                </div>
            </div>
            
            <!-- Quick Links -->
            <div>
                <h3 class="text-base sm:text-lg font-semibold mb-4 text-white">Quick Links</h3>
                <nav aria-label="Footer navigation">
                    <ul class="space-y-2">
                        <li>
                            <a href="{{ route('home') }}" 
                               class="text-gray-300 hover:text-white focus:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md text-sm sm:text-base">
                                Home
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('about') }}" 
                               class="text-gray-300 hover:text-white focus:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md text-sm sm:text-base">
                                About
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('projects.index') }}" 
                               class="text-gray-300 hover:text-white focus:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md text-sm sm:text-base">
                                Projects
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('blog.index') }}" 
                               class="text-gray-300 hover:text-white focus:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md text-sm sm:text-base">
                                Blog
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('services') }}" 
                               class="text-gray-300 hover:text-white focus:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md text-sm sm:text-base">
                                Services
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
            
            <!-- Services -->
            <div>
                <h3 class="text-base sm:text-lg font-semibold mb-4 text-white">Services</h3>
                <ul class="space-y-2" role="list" aria-label="Services offered">
                    <li class="text-gray-300 text-sm sm:text-base" role="listitem">Web Development</li>
                    <li class="text-gray-300 text-sm sm:text-base" role="listitem">3D Modeling</li>
                    <li class="text-gray-300 text-sm sm:text-base" role="listitem">UI/UX Design</li>
                    <li class="text-gray-300 text-sm sm:text-base" role="listitem">Retopology</li>
                    <li class="text-gray-300 text-sm sm:text-base" role="listitem">Rigging & Animation</li>
                    <li class="text-gray-300 text-sm sm:text-base" role="listitem">3D Rendering</li>
                </ul>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="border-t border-gray-800 mt-8 pt-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6 mb-4 md:mb-0">
                    <div class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                        <a href="mailto:{{ $footer['meta']['email'] ?? '<EMAIL>' }}" class="hover:text-white transition-colors duration-200">
                            {{ $footer['meta']['email'] ?? '<EMAIL>' }}
                        </a>
                    </div>
                    
                    <div class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <span>Available Worldwide (Remote)</span>
                    </div>
                </div>
                
                <!-- Copyright -->
                <div class="text-gray-400 text-sm">
                    <p>
                        @if(isset($footer['meta']['copyright']))
                            {{ $footer['meta']['copyright'] }}
                        @else
                            &copy; {{ date('Y') }} Aziz Khan. All rights reserved.
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Back to Top Button -->
    <button id="back-to-top" 
            class="fixed bottom-4 right-4 sm:bottom-8 sm:right-8 bg-primary-600 hover:bg-primary-700 focus:bg-primary-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 invisible focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 z-40 touch-manipulation"
            aria-label="Scroll back to top of page"
            title="Back to top">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
        </svg>
    </button>
</footer>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const backToTopButton = document.getElementById('back-to-top');
    
    if (backToTopButton) {
        let isScrolling = false;
        
        // Throttled scroll handler for better performance
        function handleScroll() {
            if (!isScrolling) {
                window.requestAnimationFrame(function() {
                    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
                    const shouldShow = scrollPosition > 300;
                    
                    if (shouldShow) {
                        backToTopButton.classList.remove('opacity-0', 'invisible');
                        backToTopButton.classList.add('opacity-100', 'visible');
                        backToTopButton.setAttribute('tabindex', '0');
                    } else {
                        backToTopButton.classList.add('opacity-0', 'invisible');
                        backToTopButton.classList.remove('opacity-100', 'visible');
                        backToTopButton.setAttribute('tabindex', '-1');
                    }
                    
                    isScrolling = false;
                });
                isScrolling = true;
            }
        }
        
        // Show/hide back to top button based on scroll position
        window.addEventListener('scroll', handleScroll, { passive: true });
        
        // Smooth scroll to top when button is clicked
        function scrollToTop() {
            // Announce to screen readers
            const announcements = document.getElementById('sr-announcements');
            if (announcements) {
                announcements.textContent = 'Scrolling to top of page';
            }
            
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            
            // Focus management - focus the skip link after scrolling
            setTimeout(function() {
                const skipLink = document.querySelector('a[href="#main-content"]');
                if (skipLink) {
                    skipLink.focus();
                }
            }, 500);
        }
        
        backToTopButton.addEventListener('click', scrollToTop);
        
        // Keyboard support
        backToTopButton.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                scrollToTop();
            }
        });
        
        // Initial state
        backToTopButton.setAttribute('tabindex', '-1');
    }
});
</script>
@endpush