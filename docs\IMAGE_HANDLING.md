# Image Handling and Optimization

This document describes the image handling and optimization system implemented for the portfolio website.

## Overview

The image handling system provides:
- Image upload with validation
- Automatic thumbnail generation
- WebP format support with fallbacks
- Lazy loading for performance
- Image optimization and compression
- Multiple size variants

## Components

### ImageService

The `ImageService` class handles all image processing operations:

```php
use App\Services\ImageService;

$imageService = app(ImageService::class);
$paths = $imageService->uploadImage($file, 'projects');
```

#### Methods

- `uploadImage(UploadedFile $file, string $directory, array $sizes = [])` - Upload and process image
- `deleteImage(string $imagePath)` - Delete image and all variants
- `getImageUrl(string $imagePath, bool $preferWebp = true)` - Get image URLs with WebP support
- `getThumbnailUrl(string $imagePath, bool $preferWebp = true)` - Get thumbnail URLs

### Blade Components

#### Lazy Image Component

```blade
<x-lazy-image 
    src="{{ asset('images/projects/image.jpg') }}"
    webp="{{ asset('images/projects/image.webp') }}"
    alt="Project image"
    class="w-full h-64 object-cover"
/>
```

#### Optimized Image Component

```blade
<x-optimized-image 
    path="projects/image.jpg"
    alt="Project image"
    class="w-full h-64 object-cover"
    thumbnail="true"
/>
```

#### Image Upload Component

```blade
<x-image-upload 
    name="thumbnail"
    label="Project Thumbnail"
    :required="true"
    :multiple="false"
    :current-image="$project->thumbnail ?? null"
    help="Upload a high-quality image for the project thumbnail"
/>
```

### Form Validation

Image uploads are validated using the `ImageUploadRequest` class:

```php
use App\Http\Requests\ImageUploadRequest;

public function store(ImageUploadRequest $request)
{
    // Image is already validated
    $file = $request->file('image');
}
```

#### Validation Rules

- **File Types**: JPEG, JPG, PNG, GIF, WebP
- **Max Size**: 5MB
- **Min Dimensions**: 100x100 pixels
- **Max Dimensions**: 4000x4000 pixels

## Directory Structure

```
public/images/
├── projects/
│   ├── thumbs/
│   └── [custom-sizes]/
├── blog/
│   ├── thumbs/
│   └── [custom-sizes]/
├── testimonials/
│   ├── thumbs/
│   └── [custom-sizes]/
└── general/
    ├── thumbs/
    └── [custom-sizes]/
```

## Image Processing

### Automatic Processing

When an image is uploaded, the system automatically:

1. Validates the file
2. Generates a unique filename
3. Creates optimized original version
4. Generates WebP version
5. Creates thumbnail (400x300)
6. Creates WebP thumbnail
7. Generates custom sizes if specified

### Custom Sizes

You can specify custom sizes when uploading:

```php
$sizes = [
    'medium' => ['width' => 800, 'height' => 600],
    'large' => ['width' => 1200, 'height' => 900]
];

$paths = $imageService->uploadImage($file, 'projects', $sizes);
```

## WebP Support

The system automatically generates WebP versions of all images for better performance. The frontend includes WebP detection and fallback support.

### Browser Support Detection

```javascript
// Automatically detects WebP support and adds class to <html>
// Classes: 'webp' or 'no-webp'
```

### Usage in Templates

```blade
<picture>
    <source srcset="{{ asset('images/projects/image.webp') }}" type="image/webp">
    <img src="{{ asset('images/projects/image.jpg') }}" alt="Project image">
</picture>
```

## Lazy Loading

All images use lazy loading by default to improve page performance:

```blade
<x-lazy-image 
    src="{{ $imageUrl }}"
    alt="{{ $alt }}"
    loading="lazy"
/>
```

### Features

- Intersection Observer API
- Loading placeholder
- Smooth fade-in animation
- Error handling
- Loading shimmer effect

## JavaScript Integration

### Image Handler

The `ImageHandler` class provides:

- Drag and drop upload
- Image preview
- Client-side validation
- File compression (optional)

### Usage

```javascript
// Automatically initialized on DOM load
// Handles all file inputs with accept="image/*"
```

## CSS Styling

Image-related styles are in `resources/css/images.css`:

- Lazy loading animations
- Upload zone styling
- Preview components
- Responsive image grids
- Loading states
- Error states

## Performance Optimizations

1. **WebP Format**: Smaller file sizes with same quality
2. **Lazy Loading**: Images load only when needed
3. **Thumbnails**: Smaller versions for listings
4. **Compression**: Optimized quality settings
5. **Responsive Images**: Multiple sizes for different screens

## Error Handling

### Server-side

- File validation with custom error messages
- Graceful fallback when GD extension unavailable
- Directory creation with proper permissions
- File cleanup on errors

### Client-side

- Real-time validation feedback
- Preview generation
- Drag and drop visual feedback
- Error state styling

## Configuration

### ImageService Settings

```php
protected int $maxFileSize = 5242880; // 5MB
protected int $thumbnailWidth = 400;
protected int $thumbnailHeight = 300;
protected int $jpegQuality = 85;
protected int $webpQuality = 80;
```

### Allowed MIME Types

```php
protected array $allowedMimeTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
];
```

## Usage Examples

### Admin Project Upload

```blade
<form method="POST" enctype="multipart/form-data">
    <x-image-upload 
        name="thumbnail"
        label="Project Thumbnail"
        :required="true"
        :current-image="$project->thumbnail"
    />
    
    <x-image-upload 
        name="project_images"
        label="Project Gallery"
        :multiple="true"
        help="Upload multiple images for the project gallery"
    />
</form>
```

### Frontend Display

```blade
<div class="project-gallery">
    @foreach($project->images as $image)
        <x-optimized-image 
            :path="$image"
            alt="Project gallery image"
            class="w-full h-64 object-cover rounded-lg"
        />
    @endforeach
</div>
```

### Blog Post Images

```blade
<article class="blog-post">
    <x-optimized-image 
        :path="$blog->thumbnail"
        alt="{{ $blog->title }}"
        class="w-full h-96 object-cover mb-8"
    />
    
    <div class="prose">
        {!! $blog->content !!}
    </div>
</article>
```

## Testing

Run image-related tests:

```bash
php artisan test --filter=ImageService
```

Note: Some tests require the GD PHP extension for full functionality.

## Troubleshooting

### Common Issues

1. **GD Extension Missing**: Install php-gd extension
2. **Permission Errors**: Check directory permissions (755)
3. **File Size Limits**: Adjust PHP upload_max_filesize
4. **Memory Limits**: Increase PHP memory_limit for large images

### Debug Mode

Enable debug logging in `ImageService`:

```php
\Log::info('Image processing', ['file' => $filename, 'size' => $file->getSize()]);
```